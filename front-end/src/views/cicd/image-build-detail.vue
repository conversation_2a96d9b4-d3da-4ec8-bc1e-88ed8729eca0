<template>
  <div class="app-container image-build-detail" style="min-height: 1200px;">
    <div style="width: 960px;margin: 0 auto;">
      <el-steps :active="2" align-center>
        <el-step v-for="(item,index) in tasks" :title="item.title" description="" :status="item.status"
                 :icon="item.icon"
                 style="cursor: pointer;"
                 @click.native="stepClick(index)">
          <template slot="description">
            {{ item.statusDesc }}
            <div v-if="job.status.toUpperCase() === 'WAIT'" style="display: inline-block;">
              <el-tooltip class="item" effect="dark" content="前置任务成功，开始执行当前任务；前置任务失败，当前任务取消" placement="top">
                <el-button type="text" @click="jobDetailPage(job.beforeJobId)" style="font-size: 12px;padding: 0;">
                  (查看前置任务)
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-step>
      </el-steps>
    </div>
    <div style="position: relative;height: 20px;">
      <div style="float: right; padding-bottom: 5px;">
        <el-button
          v-if="['RUNNING','WAIT'].includes(this.job.status)"
          type="text"
          style="padding: 0"
          icon="el-icon-switch-button"
          @click="cancel()">取消任务
        </el-button>
        <el-button
          v-else
          type="text"
          style="padding: 0"
          icon="el-icon-refresh-left"
          @click="redo()">重新构建
        </el-button>
        <el-button
          type="text"
          style="padding: 0"
          icon="el-icon-search"
          @click="imagePage()">查看镜像
        </el-button>
      </div>
      <div style="font-size: 14px;font-weight: bold;color: rgb(64, 158, 255);float: left">
        信息
        <div style="margin-left: 60px;display: inline-block;">
          <el-button
            type="text"
            style="padding: 0;color:#ccc;font-size: 10px;"
            @click="setJobFailed()">设置为失败
          </el-button>
          <el-button
            type="text"
            style="padding: 0;color:#ccc;font-size: 10px;"
            @click="setJobSuccess()">设置为成功
          </el-button>
        </div>
      </div>
    </div>
    <el-card class="box-card" style="clear: both">
      <el-descriptions :column="3" labelClassName="desc-label" contentClassName="desc-content">
        <el-descriptions-item label="Git地址">{{ this.job.params.gitUrl }}</el-descriptions-item>
        <el-descriptions-item label="Git模块">{{ this.job.params.gitModule }}</el-descriptions-item>
        <el-descriptions-item label="Git分支或Tag">{{ this.job.params.gitTag }}</el-descriptions-item>
        <el-descriptions-item label="CommitId">{{ this.job.params.commitId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="Maven镜像">{{ this.job.params.mavenImage }}</el-descriptions-item>
        <el-descriptions-item label="父POM">{{ this.job.params.parentPom }}</el-descriptions-item>
        <el-descriptions-item label="镜像版本">{{ this.job.params.artifactImage ? this.job.params.artifactImage.split(":").pop() : '-' }}</el-descriptions-item>
        <el-descriptions-item label="依赖包版本检测">{{ this.job.params.dependencyCheck }}</el-descriptions-item>
        <el-descriptions-item label="强制编译">{{ this.job.params.forceCodeCompile }}</el-descriptions-item>
        <el-descriptions-item label="单元测试">{{ this.job.params.unitTest }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ this.job.remark }}</el-descriptions-item>
        <el-descriptions-item label="操作人">{{ this.job.author }}</el-descriptions-item>
        <el-descriptions-item label="操作时间">{{ this.job.createdAt }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <div style="margin-top: 20px;font-size: 14px;font-weight: bold;color: rgb(64, 158, 255);padding-bottom: 3px;">输出日志</div>
    <el-card class="box-card" style="margin-top: 10px;">
      <el-tabs tab-position="top">
        <el-tab-pane :label="item.title" v-for="(item,index) in tasks">
          <div v-if="item.type ==='BUILD'">
            <el-tabs type="border-card" style="margin-top: 10px;" value="jenkinsTab2">
              <el-tab-pane name="jenkinsTab1" label="纯文本日志">
                <pre style="white-space: pre-wrap" class="el-collapse-item__content">{{ item.output }}</pre>
              </el-tab-pane>
              <el-tab-pane name="jenkinsTab2">
                <template v-if="item.attributes.jenkinsBuildId">
                    <span slot="label">Jenkins页面 <small style="color:#aaa;">
                    (如果没自动刷新，点击<el-button type="text" size="mini" @click="jenkinsPageReload()">手动刷新</el-button>)
                    (<el-button style="margin-left: 0" type="text" size="mini"
                                @click="jenkinsPage(item.attributes.jenkinsBuildId,item.attributes.jenkinsJob)">新窗口打开</el-button>)</small>
                  </span>
                  <iframe id="jenkins_iframe" style="width: 100%;min-height: 800px;border: solid 1px #eee;"
                          :src="jenkinsUrl(item.attributes.jenkinsBuildId,item.attributes.jenkinsJob)"></iframe>
                </template>
                <template v-else>
                  <span slot="label">Jenkins页面</span>
                  <div></div>
                </template>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div v-else>
            <pre style="white-space: pre-wrap;padding-left: 10px;" class="el-collapse-item__content">{{ item.output }}</pre>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <div>
      <el-backtop></el-backtop>
    </div>
  </div>
</template>

<script>

import {cancelJob, findJobById, findTaskByJobId, redoJob, setJobStatus} from "@/api/job";

export default {
  data() {
    return {
      timerId: null,
      job: {
        params: {}
      },
      tasks: [],
      activeNames: [0],
      autoChangeActiveNames: true,
      runningExecutions: [],
      jenkinsTable: "jenkinsTab2"
    }
  },
  components: {},
  computed: {},
  beforeDestroy: function () {
    if (this.timerId) {
      clearTimeout(this.timerId);
      console.log("clear timer, id:" + this.timerId);
    }
  },
  mounted() {
    let id = this.$route.query.jobId
    if (id) {
      this.loadJob(id)
    }
  },
  methods: {
    loadJob(id) {
      findJobById(id).then(response => {
        this.job = response.data;
        this.loadTasks(id)
        let status = this.job.status.toUpperCase();
        let vThis = this;
        if (status === "RUNNING") {
          this.timerId = setTimeout(function () {
            vThis.loadJob(id);
          }, 5000);
        }
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    loadTasks(jobId) {
      findTaskByJobId(jobId).then(response => {
        this.tasks = response.data
        this.modifyStages()
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    stepClick(stepIndex) {
      if (this.activeNames.includes(stepIndex)) {
        this.activeNames = [];
      } else {
        this.activeNames = [stepIndex];
      }
    },
    modifyStages() {
      for (let item of this.tasks) {
        item.status = this.toStepStatus(item.status)
        if (item.status === "process") {
          item.icon = "el-icon-loading"
        } else {
          item.icon = ""
        }
      }
    },
    cancel() {
      this.$confirm(`是否继续取消？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        cancelJob(this.job.id).then(response => {
          this.$message.success('操作成功！');
          this.loadJob(this.job.id)
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }).catch((e) => {
        console.error(e);
      });
    },
    setJobFailed() {
      setJobStatus(this.job.id, "fail").then(response => {
        this.$message.success('操作成功！');
        this.loadJob(this.job.id)
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    setJobSuccess() {
      setJobStatus(this.job.id, "success").then(response => {
        this.$message.success('操作成功！');
        this.loadJob(this.job.id)
      }).catch((e) => {
        this.$message.error(e.message);
      });
    },
    redo() {
      this.$confirm(`确认使用当前参数重新构建吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        redoJob(this.job.id).then(response => {
          let id = response.data.id
          let routeUrl = this.$router.resolve({query: {"jobId": id}})
          window.location.href = routeUrl.href
          window.location.reload();
        }).catch((e) => {
          this.$message.error(e.message);
        });
      }).catch((e) => {
        console.error(e);
      });
    },
    jobDetail(jobId) {
      this.$router.push({
        name: 'image-build-detail', query: {"jobId": jobId}
      });
      self.location.reload()
    },
    jenkinsPage(buildId, job) {
      window.open(this.jenkinsUrl(buildId, job));
    },
    jenkinsUrl(buildId, job) {
      let url = `/api/page/redirect?type=jenkins&buildId=${buildId}`
      if (job) {
        url = url + "&job=" + job
      }
      return url;
    },
    imagePage() {
      let routeUrl = this.$router.resolve({name: 'cicd-image-list', query: {"gitUrl": this.job.params.gitUrl, "gitModule": this.job.params.gitModule}})
      window.open(routeUrl.href, '_blank');
    },
    jenkinsPageReload() {
      //jenkins的pipeline页面有事不会自动刷新
      document.getElementById("jenkins_iframe").src += "";
    },
    jobDetailPage(jobId) {
      findJobById(jobId).then(response => {
        const job = response.data;
        const name = job.type === 'CD' ? 'cicd-app-deploy-detail' : 'cicd-image-build-detail';
        let rou = this.$router.resolve({name: name, query: {"jobId": jobId}});
        window.open(rou.href, '_blank');
      }).catch((e) => {
        this.$message.error(e.message);
      })
    },
    toStepStatus(stageStatus) {
      let ret = ""
      switch (stageStatus) {
        case "WAIT":
        case "SKIP":
        case "CANCEL":
          ret = "wait"
          break;
        case "RUNNING":
          ret = "process"
          break;
        case "FAIL":
          ret = "error"
          break;
        case "SUCCESS":
          ret = "success"
          break;
        default:
          ret = "finish"
      }
      return ret
    },
  }
}
</script>

<style>
.image-build-detail .el-collapse-item__header {
  background-color: #eee;
}

.image-build-detail .el-collapse-item__wrap {
  border: #eee solid 2px;
  padding-left: 10px;
}

/*.el-step__head.is-process, .el-step__title.is-process {*/
/*  color: #409EFF!important;*/
/*}*/

.image-build-detail .el-collapse-item__arrow.el-icon-arrow-right {
  position: absolute;
  left: 10px;
  font-size: 1.2em;
  font-weight: bold;
}

.image-build-detail .el-collapse-item__header {
  position: relative;
  padding-left: 40px;

}

.image-build-detail .app_info_panel {
  color: #909399;
  margin: 10px 0;
  font-size: 14px;
}

.image-build-detail .app_info_panel + .app_info_panel {
  margin-left: 30px;
}

.image-build-detail .app_info_panel > b {
}


.image-build-detail .divider-blockquote {
  padding-bottom: 16px;
}

.image-build-detail .divider-blockquote > span {
  color: #409EFF;
  margin-left: 5px;
  font-weight: bold;
}

.image-build-detail .divider-blockquote > .el-divider {
  background-color: #409EFF;
  margin: 3px 0 5px;
}

.image-build-detail .divider-blockquote.qa > span {
  color: #999;
}

.image-build-detail.divider-blockquote.qa > .el-divider {
  background-color: #bbb;
}

.image-build-detail .divider-blockquote.qa span.new-line {
  padding: 5px 10px 0;
  display: block
}

.image-build-detail .divider-blockquote .divider-content {
  padding: 0 10px;
}

.image-build-detail .app-info-tab {
  padding: 5px 0;
  font-size: 14px;
}

.image-build-detail .app-info-tab > label {
  color: #99a9bf;
}

.image-build-detail .app-info-tab > span {
  color: #606266;
  word-break: break-all;
}

.image-build-detail .el-card__body {
  padding: 10px;
}

.image-build-detail .desc-label {
  color: #99a9bf;
  font-weight: 700;
}

.image-build-detail .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 0;
}
</style>
