import json
from datetime import datetime
from pathlib import Path

import requests
from requests.auth import HTT<PERSON><PERSON>asic<PERSON>uth


def image_exist(repository, image):
    """
    镜像是否存在于镜像仓库中
    """
    url = f"https://reg.foneshare.cn/api/v2.0/projects/{repository.split('/')[0]}/repositories/{'/'.join(repository.split('/')[1:]).replace('/', '%252F')}/artifacts/{image}"

    response = requests.get(url, auth=HTTPBasicAuth("robot$k8s-app-manager", "hnTVEaHCFJAt4XUG6Q6GxmXm10eBRTyk"))
    if response.status_code == 200:
        return True
    elif response.status_code == 404:
        return False
    else:
        response.raise_for_status()


all_images = []
for it in json.load(open("fs-image-preheat-job.json")).get("spec").get("containers")[0].get("env"):
    name = it.get("name")
    if "IMAGE_PATH_" not in name:
        continue
    if not it.get("value"):
        continue
    all_images.append(it.get("value"))

images = []
for it in all_images:
    # img_without_host = it.replace("apdb-harbor.foneshare.cn:30040/foneshare-proxy/", "")
    # repository = img_without_host.split(":")[0]
    # tag = img_without_host.split(":")[1]
    # if not image_exist(repository, tag):
    #     print("image not exist, will remove: " + it)
    #     continue
    images.append(it)


def split_array(array, group_size):
    return [array[i:i + group_size] for i in range(0, len(array), group_size)]


# 分组
grouped_arrays = split_array(images, int(len(images) / 8) + 1)

curr_date = datetime.now().strftime('%Y%m%d')
Path(curr_date).mkdir(parents=True, exist_ok=True)

# 打印结果
for i, group in enumerate(grouped_arrays):
    item = json.load(open("image-preheat-job-template.json"))
    job_name = "image-preheat-job-{}-{}".format(curr_date,i)
    item["metadata"]["name"] = job_name
    init_containers = []
    for ig_idx, ig in enumerate(group):
        init_containers.append({
            "name": "image-index-init-{}-{}".format(i, ig_idx),
            "image": ig,
            "imagePullPolicy": "Always",
            "command": ["echo", "success"]
        })
    item["spec"]["template"]["spec"]["initContainers"] = init_containers

    with open(curr_date + "/" + job_name + ".json", 'w', encoding='utf-8') as f:
        f.write(json.dumps(item, indent=2))
