{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "image-preheat-job-20250623-5", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-5-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/db-copier:hotfix---get_metadata_sql_specify_table--202503071302", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/db-metric:v5.7.55-master-20240521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/es-syncer:v5.7.55-master-20240521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/pg-scanner:v5.7.45-split-query-master-20240128", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/scanner-ui:v5.7.55-master-20240521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-cep/fs-cep-provider:release-9.5.0--202503212328", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-customer-component/fs-customer-component-provider:master--202504122330", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-ext-contact/fs-ext-contact-provider:master-202410262342", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-id/fs-id-server:v2.0.0-springboot-20240506", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-notice/fs-notice-provider:release-955--202505212315", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-paas-wishful/fs-paas-recording-provider:master--202503281848", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-paas-wishful/fs-paas-wishful-ocr-web:master--202505221430", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-bot-crm-helper:master--202503281848", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-bot-manage:master--202505221401", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-bot-provider:master--202505221442", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-extension-biz:master--202505091118", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-extension-provider:master--202505141456", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-objgroup-manage-provider:master--202503281848", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-plugin-provider:master--202505221436", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-search-message-provider:master--202503281848", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-extension/fs-qixin-search-provider:master--202503281848", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin-task/fs-qixin-task-provider:master--202505221400", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-online-manage-provider:master-202407262109", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-biz-web:master--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-provider-cloud:master--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-qixin/fs-qixin-web:master--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-scheduler-task/fs-scheduler-task-provider:v1.0.157-feature---950_wait_notify-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/fs-webpage-customer/fs-webpage-customer-provider:v9.5.5-all-01-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/javaserviceconsole/fs-console-web:master", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/qixin/javaserviceconsole/fs-console-web:master--202504032239", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/wechat-union/fs-wechat-union/fs-wechat-proxy-callback:v9.5.0-master-20250412-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/wechat-union/fs-wechat-union/fs-wechat-sender-task:v9.5.0-master-20250412-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/wechat-union/fs-wechat-union/fs-wechat-union-all:v9.5.0-master-20250412-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/checkins/checkins-biz:master--202505221544", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-office-v2/checkins-office-v2-server:cloud--202503071411", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/appserver/fs-appserver-checkins-v2/checkins-v2-task:huaweicloud--202505221420", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/er-manager-all/er-manager-deploy:v930-main-20250412-2", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-5-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-linkapp-provider:v9.5.5-master-20250523-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}