{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "image-preheat-job-20250623-2", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-2-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-stage-propeller/fs-stage-propeller-console:master", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-workflow/fs-workflow-cloud:v9.5.0-online-v1-release---9.5.0-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-workflow/fs-workflow-console:master", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-all/fs-crm-all-web:v9.5.5-all-01-develop---9.5.5-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-recycling-task/fs-crm-recycling-task-web:develop---9.5.5--202505221150", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task-sfa/fs-crm-task-sfa-web:v9.5.5-all-develop---9.5.5-20250523", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm-task/fs-crm-task-web:develop---9.5.0--202505221406", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:master--202505221406", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm/fs-crm/fs-crm-web:v9.5.5-all-01-cglib-develop---9.5.5-20250525", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-metadata/fs-crm-metadata-web:v9.5.5-all-develop---9.5.5-20250523", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-smartform/fs-crm-smartform-cgi:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-smartform/fs-crm-smartform-web:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-template/fs-crm-template-web:v9.5.5-01-master-20250522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-cloud:master--202504220142", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/crm_java/fs-crm-workflow/fs-crm-workflow-console:master", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/cloud-controll-center/controll-cgi:master-cloud-202409252257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/cloud-controll-center/controll-console:master-cloud-202409261816", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-industry-interface/fs-bi-industry-interface-provider:v2.2.1120-release-20240307", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/dataplatform/fs-bi-udf-report/fs-bi-udf-report-web:v2024.11.86-master-20250525", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/devops/fs-k8s-tomcat-test/fs-k8s-tomcat-test-biz:master-202408221652", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-mq-listener:develop---9.6.0_online_consult--202506132230", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/e-service/fs-e-service/fs-eservice-web:develop---9.6.0_online_consult--202506132230", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-biz:release-955--202505221201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-lifecycle:release-955--202505221423", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-next-provider-web:release-955--202505221404", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-notice:release-955--202505221423", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-feeds/fs-feeds-provider:release-955--202505221201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-new-schedule/fs-new-schedule-web:master--202505220018", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-task-web:master--202505302342", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/feeds/fs-social-feeds/fs-social-web:master--202506112314", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-cloud/kafka2hdfs/fs-bi-bizlog2ch:chaws--202504190011", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/gateway-log2es:master--202505162326", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/server-log2clickhouse:master--202505162326", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-eye/fs-eye-consumer/server-log2es:master--202505162326", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-job/xxl-job/fs-job-admin:master-202411131922", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-provider:develop---9.5.5---marketing_email--202505221145", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-task:develop---9.5.5---marketing_email--202505221145", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-2-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-email-proxy/fs-open-email-proxy/fs-open-email-proxy-web:develop---9.5.5---marketing_email--202505221145", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}