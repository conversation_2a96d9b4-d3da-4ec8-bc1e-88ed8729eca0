{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "image-preheat-job-20250623-0", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-0-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/bizcommon/egress-api-ext-resource:2.6", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-eye/fs-eye-radar:v1.1.2-main-20250509", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-alpha/fs-open/oauth/fs-oauth-base-provider:f_master-202411141614", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-query-service:v9.5.4-develop---9.5.0-20250419", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/auditlog/auditlog-service:v9.4.5-all-04-develop---9.4.5-20250328", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-ai-detector/fs-ai-detector-provider:ai_model--202504130140", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-office-v2/checkins-office-v2-task:cloud--202501161127", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-office-v2/fs-appserver-holiday-v2-server:cloud--202412061753", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-mq-dispatcher:master--202504130213", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-mq-dispatcher:master--20**********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-checkins-v2/checkins-v2-server:master--20**********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-appserver-schedule/fs-appserver-schedule-provider:release---945--202503281526", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-crm-fmcg/fs-crm-fmcg-service:master--202505231808", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-efficiency/fs-fmcg-efficiency-cgi:master--202505221402", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg-sales/fs-fmcg-sales-cgi:master--202505221124", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-fmcg/fs-fmcg-service:dev-9.5.0--202504152301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-integral/fs-integral-provider:dev-9.5.5--202505221150", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/appserver/fs-integral/fs-integral-task:dev-9.3.0--202412132301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-adapter-hj:master-cloud--202505221157", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-adapter:master-cloud--202505221157", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-biz:master-cloud--202505221157", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/baseservice/fs-organization/fs-organization-provider:master-cloud--202505221157", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-dim-online:cloud-dispatcher--202412132305", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-offline:v1.0.5-cloud-dispather-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-online-calculate:v2.2.1120-cloud-dispather-20241103", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-online:v2.2.1120-cloud-dispather-20241103", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-custom-statistic-paas2bi-transfer:v1.0.5-cloud-dispather-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/crm-bi-custom-statistic/fs-bi-feed2pg-online:v2.2.1120-cloud-dispather-20241103", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-crm-report/fs-bi-crm-report-web:v2025.01.36-master-20250525", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-dev-platform/fs-bi-dev-web:v2024.11.78-master-20250525", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-export:v2024.11.36-master-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-goal/fs-bi-goal-web:v2025.1.20-master-20250525", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-org/fs-bi-org-entrance:v2.0.169-master-20241220", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-permission/fs-bi-permission-access:v2024.12.12-master-20241220", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-stat-transfer:v1.0.62-master-20250515", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-uitype:v2024.11.45-master-20250525", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi-warehouse/warehouse-dws:v1.7.12-main-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-0-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-devops:v2025.01.64-master-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}