{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "image-preheat-job-20250623-3", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-3-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open-si/erpdss-outer-connector/connector-runtimev2-fs:v950-main-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-erp-sync-data/erp-sync-data-whole-war:v950.13fix2-v950-20250514", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-fcp:develop---9.4.0--202412312337", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-online-consult/fs-online-consult-web:develop---9.6.0--202506140144", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-center-provider:v9.5.5-all-01-develop---9.5.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-center-web:v9.5.5-all-01-develop---9.5.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-app-manage:v9.5.5-all-01-develop---9.5.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-app-center/fs-open-center-fcp-provider:v9.5.5-all-01-develop---9.5.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-qywx-gateway/qywx-account-bind-provider:tag-master_gray-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-qywx-gateway/qywx-account-sync-provider:tag-master_gray-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-qywx-gateway/qywx-event-handler-web:tag-master_gray-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-qywx-gateway/qywx-message-save-provider:tag-master_gray-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-open/fs-open-qywx-gateway/qywx-message-send-provider:tag-master_gray-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/fs-sail/fs-sail-order/fs-sail-order-web:master--202504132341", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-app-view/fs-app-view-biz:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-app-view/fs-app-view-cgi:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-document-converter/fs-document-convert-web:3.5.4-unify-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-experience-account/fs-experience-account-provider:release-9.5.0--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-experience-account/fs-sandbox-biz:release-9.5.0--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-plat-org-adapter/fs-plat-org-adapter-provider:master--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-plat-service/fs-plat-service-biz:release-955--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-plat-service/fs-plat-service-provider:release-950--************", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-polling/fs-polling-cgi:master--202501162329", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-polling/fs-polling-provider:master--202501162329", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-task-manage/fs-task-manage-provider:v1.0.4-master-20240320", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/infrastructure/fs-user-extension/fs-user-extension-biz:v9.5.5-all-01-develop---9.5.5-********", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/javacommon/logconf/logconfig-web:master-202407142001", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-global-transaction/global-transaction-server:v9.0.0-gray-02-develop---9.0.0-20240410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-crm-notify-provider:master--202505252215", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-message-server-wrapper:release---950--********1132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-message/fs-todo-provider:master--202506062341", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata-option/fs-metadata-option-provider:v1.0.9-feature-batch-create-v2-20231117", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:dev-9.4.5--202503082340", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-metadata-service:dev-9.5.5--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-metadata/fs-paas-reference-service:master--202503122303", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-action-centre/fs-paas-action-web:feature---action_list--202504122331", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-provider:release---9.6.0--********1825", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-3-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-ai/fs-paas-ai-task:feature---rag_dispatcher_task--********0015", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}