{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "image-preheat-job-20250623-4", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-4-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-api-bus/fs-apibus-global:v8.7.37-support-499-errorlog-master-20241022", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-api-bus/fs-apibus-ncrm:v8.7.37-support-499-errorlog-master-20241022", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-api-bus/fs-apibus-paas:v8.7.37-support-499-errorlog-master-20241022", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-app-task/fs-paas-app-task-web:v9.5.5-all-01-master-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-biz:release-950--202504092330", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-provider:release-950--202504092330", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-auth/fs-paas-auth-task:1-release-950-20250328", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-bizconf/fs-paas-bizconf-web:master-202410181912", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-calculate-task/fs-paas-calculate-task-web:v9.5.5-all-01-develop---9.5.5-20250521", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-data-auth/service:feature---refresh-team-auth--202505161922", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-data-auth/worker:feature---refresh-team-auth--202505161924", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-data-tools/tenant-sandbox:feature---dev-9.5.0--202503212319", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-function-engine/fs-paas-function-service-provider:release---9.5.0--202504092301", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-gnomon/fs-paas-gnomon-executor:feature---supplement_log--202502212257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-job-schedule/fs-paas-job-schedule-web:v6.7.65-feature---950_waiting_notify-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-leica/paas-leica-sync:feature---dev-9.5.5--202504290048", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-license/fs-paas-license-provider:feature---dev-950--202503300023", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-license/fs-paas-license-surrogate:feature---dev-950--202503212313", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-metadata-dataloader:v9.5.0-all-01-develop---9.5.0-20250410", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-org/fs-paas-org-provider:feature---ignore_db_exception--202505221132", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-recycle:feature---dev-9.5.0--202503212326", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-refresh/fs-paas-refresh-forest:master--202505221151", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-resource/resource-starter:main--202503212234", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-rule/fs-paas-rule-service-provider:feature---950_rule_right_value_matching_fromMaster--202504101930", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-score/fs-paas-score-biz:v9.4.5-all-01-develop---9.4.5-20250306", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-service/paas-describe-web:feature---dev-915-202410121843", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-cloud:v9.5.20-hotfix---check_cancel_instance_after-20250611", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-console:v9.3.6-release---9.4.0-20250117", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-paas-workflow/fs-paas-workflow-provider:v9.5.12-release---9.6.0_oneFlow-20250516", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-pod/fs-pod-console:hotfix---bug_repair--202506221808", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-pod/fs-pod-service:feature---ch-db-optimize--202504130102", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-pod/pod-console:hotfix---switch_route_add_pgbouncer--202506092302", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/fs-refresh-es-data/refresh-es-index:dev-950--202503260034", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/i18n-setting:develop---9.5.0--202504101930", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-console:feature---optimize_data_validate--202505211848", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-operator/paas-db-provider:feature---fix-clickhouse-timeout--202503272313", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/bi-transfer:v5.7.45-split-query-master-20240128", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-4-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/paas/paas-db-scanner/db-backup:v5.7.55-master-20240521", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}