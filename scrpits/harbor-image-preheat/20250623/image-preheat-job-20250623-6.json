{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "image-preheat-job-20250623-6", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-6-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-relation-biz:v9.5.5-master-20250523-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-relation-channel-web:v9.5.5-master-20250525-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-relation-global-rest:v9.5.5-master-20250523-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-relation-http:v9.5.5-master-20250523-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/fs-enterprise-relation/fs-enterprise-relation-rest-web:v9.5.5-master-20250523-01", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/fs-sync-data-all:v956-vip-20250524-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/sync-data-admin-system/sync-data-admin-web:v910-master-20240915-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/sync-data-mq-provider:v956-vip-20250524-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/baseservice/sync-data/sync-data-task:v910-master-20240915-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-file-server:v3-jdk-fix-20250119", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone-auth:v2.4-20250328", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-audioserver:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-cgi:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-cloud:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-fileserver:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-metaserver:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-proxy:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-transfer:v885.7.5-unify-multi-cluster-20240130", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/bizcommon/fs-stone/fs-stone-upload-consumer:945-fix-20250309", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/devlab/fs-video/fs-video-processor:master--202505221201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/devlab/fs-video/fs-video-provider:master--202505221201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/devlab/fs-video/fs-video-uploader:master--202505221201", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing-statistic/fs-marketing-statistic-provider:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-provider:tag-zfy-master-20250613-v2", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-qywx:master--202506131844", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-task:master--202506131844", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web-kis:master--202506131844", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-marketing/fs-marketing/fs-marketing-web:master--202506131844", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open-msg/fs-open-msg/fs-open-msg-web:tag_main_2025_0321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open-platform/open-api-all/open-api-gateway-web:v907-master-20240727-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open-platform/open-api-all/open-api-service-allprovider:v890-20240329-master-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-erp-sync-data/fs-erp-oa:master-202411120017", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-erp-sync-data/fs-erp-oa:v940_fix_language--202503082339", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-mankeep/fs-mankeep-provider:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-open-feishu-gateway/feishu-sync-provider:main_chenzx_20240817-202408170014", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-open-feishu-gateway/feishu-web:tag-main-20240816", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-open-feishu-gateway/fs-order-contacts-proxy:tag-main-20240816", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-6-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-open-materail/fs-open-material-web:v9.5.5-special-for-sales-forecast-skip-sandbox-copy-20250525-1", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}