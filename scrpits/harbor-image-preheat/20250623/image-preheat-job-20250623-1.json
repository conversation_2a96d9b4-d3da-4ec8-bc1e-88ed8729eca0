{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "image-preheat-job-20250623-1", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-1-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-metadata-ant:v2025.01.73-master-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-metadata:v2025.01.73-master-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-mq:v2025.01.73-master-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-sqlengine:v2025.01.64-master-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-sqlgenerator:v2025.01.64-master-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-stat:v2025.01.103-master-20250525", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-task:v2025.01.64-master-20250321", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bi/fs-bi/fs-bi-transfer:v2025.01.75-release-transfer-20250413", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/async-job-center/async-job-export:v9.4.10-master-20250507", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/async-job-center/async-job-import:v9.4.4-master-20250117", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/async-job-center/paas-batch-web:master--202505221150", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/client-action-router/action-router-console:master-cloud-202409261148", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/client-action-router/action-router-service:master-cloud-202409252258", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/egress-api-service/egress-api-service:feat---mengniu-url--202505212256", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/egress-api-service/egress-api-service:v2.8.7-main-20250514", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/egress-api-service/egress-api-service:v2.9.1-main-20250613", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-disconf/config-service:master--202502101159", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-egress-proxy:v3.4.2-master-20250114", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-eye-monitor/eye-backend:master-202410121027", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-hubble/hubble-index:feature---support_outtenant_pool_fromdev930--202501161129", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-hubble/hubble-query:feature---support_outtenant_pool_fromdev930--202501161129", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-hubble/hubble-wal-log:feature---support_outtenant_pool_fromdev930--202501161129", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-cgi:v2.1.101-refactor---reduce-clear-describe-cache-20240420", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-console:feature---new_i18n_test_normal--202506201738", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-message:feature---app_load_nginx_optimize--202505221150", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-i18n/i18n-service:feature---new_i18n_test_normal--202506140035", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-mq-proxy/cloud-mq-consumer:feature---master-audit-log-202407281214", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/fs-mq-proxy/cloud-mq-proxy:feature---more-profiles--202502142023", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/weex-bundle-console/bundle-console-service:feature---master-cloud-merge-202409261441", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bizcommon/weex-bundle-console/bundle-console-web:feature---master-cloud-merge-202409261703", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm-after-action/fs-bpm-after-action-biz:hotfix---502_and_502_timeout_error--202504182249", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm-after-action/fs-bpm-after-action-console:master", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-cloud:v9.4.12-release---9.5.0-20250412", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-bpm/fs-bpm-console:master", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-34", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-flow-inspection/fs-flow-inspection-biz:v1.0.19-hotfix---update_sendMessageContent-20240703", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-35", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-flow/fs-flow-biz:v8.8.5-release-8.9.0-20240127", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-36", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-flow/fs-flow-cloud:v9.5.11-release---9.6.0_oneFlow-20250516", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-1-37", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-rc/bpm/fs-stage-propeller/fs-stage-propeller-cloud:v9.4.23-hotfix---turn_off_timeout_notice_for_stage_jump-20250611", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}