{"apiVersion": "batch/v1", "kind": "Job", "metadata": {"name": "image-preheat-job-20250623-7", "namespace": "image-preheat", "labels": {"app": "harbor-preheat"}}, "spec": {"backoffLimit": 1, "template": {"metadata": {"labels": {"app": "harbor-preheat"}}, "spec": {"affinity": {"podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["harbor-preheat"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 100}]}}, "restartPolicy": "OnFailure", "initContainers": [{"name": "image-index-init-7-0", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-outer-web:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-1", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-provider:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-2", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-task:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-3", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/fs-open/fs-training-server/fs-training-web:master--202505221200", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-4", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-document-converter/fs-document-convert-web-big:3.6.2-unify-20250424", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-5", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-document-preview-service/fs-document-preview-cgi:v940-20250214", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-6", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-user-login/fs-login-cgi:release---9.4.0--202504161522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-7", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-user-login/fs-user-login-biz:release---9.4.0--202504161522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-8", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-user-login/fs-user-login-provider:release---9.4.0--202504161522", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-9", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/infrastructure/fs-xt-proxy/fs-xt-proxy-provider:v1.2.6-master-20220628", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-10", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/paas/fs-paas-function-engine/fs-paas-function-service-background-provider:master-202408302257", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-11", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-fsc/fs-fsc-cgi:v950-20250304", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-12", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-fsc/fs-fsc-provider:v950-20250304", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-13", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-warehouse-batch/warehouse-batch-biz:v940.2-master-20250119", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-14", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/artifact/fxiaoke-parent-pom-release/qixin/fs-warehouse/fs-warehouse-provider:920.2-master-foneshare-20241105", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-15", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat10:openjdk21", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-16", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat10:openjdk23", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-17", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat10:openjdk23-v250527", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-18", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat10:openjdk24", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-19", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat10:openjdk24-v250527", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-20", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8-beefly1.0.7:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-21", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8-ffmpeg3.3.4:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-22", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8-function:dragonwell8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-23", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8-wkhtmltopdf:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-24", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:ali-dragonwell8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-25", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:openjdk17", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-26", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:openjdk21", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-27", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-28", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9-doc-converter:openjdk17", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-29", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9-python3:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-30", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9:openjdk17", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-31", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9:openjdk21", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-32", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9:openjdk21-v250326", "imagePullPolicy": "Always", "command": ["echo", "success"]}, {"name": "image-index-init-7-33", "image": "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat9:openjdk8", "imagePullPolicy": "Always", "command": ["echo", "success"]}], "containers": [{"name": "app", "image": "reg.foneshare.cn/docker.io/alpine:latest", "command": ["echo", "success"], "resources": {"limits": {"cpu": "30m", "memory": "20Mi"}, "requests": {"cpu": "30m", "memory": "20Mi"}}}]}}}}