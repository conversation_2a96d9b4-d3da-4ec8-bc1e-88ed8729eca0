apiVersion: v1
kind: ConfigMap
metadata:
  name: {{DEPLOY_APP_NAME}}-config
  namespace: {{DEPLOY_K8S_NAMESPACE}}
  annotations:
    fxiaoke.com/managed-by: gitlab-ci
data:
  config.json: |-
    {
      "app": {
        "runMode": "release",
        "httpPort": 80,
        "readTimeout": 7200,
        "writeTimeout": 7200,
        "cacheCategory": "redis",
        "runtimeDir": "runtime",
        "uploadDir": "runtime/upload",
        "downloadDir": "runtime/download",
        "kubeConfDir": "conf/kubeconf"
      },
      "jenkins": {
        "host": "https://jenkins2.foneshare.cn",
        "username": "FSSvc0032",
        "password": "ec1F\\CKzCw/udK.",
        "jobBuildImage": "k8s-app-image-build",
        "jobBuildPackageToImage": "k8s-app-package-build-to-image",
        "jobJacoco": "k8s-jacoco",
        "mavenImage": "reg.foneshare.cn/base/fs-maven3.9",
        "enableCRSF": true
      },
      "gitlab": {
        "host": "https://git.firstshare.cn",
        "token": "**************************"
      },
      "cms": {
        "webHost": "https://oss.foneshare.cn/cms",
        "apiHost": "http://************:40756/cs/api",
        "token": "k8s-manager-jnM2ShB5aE6BRBAtGSDc",
        "cmdbConfigPath": "foneshare/cmdb-mark-v2.json",
        "serviceConfigPath": "foneshare/k8s-service-list.csv"
      },
      "harbor": {
        "host": "reg.foneshare.cn",
        "appProject": "app",
        "helmChartProject": "chartrepo",
        "https": true,
        "username": "robot$k8s-app-manager",
        "password": "hnTVEaHCFJAt4XUG6Q6GxmXm10eBRTyk",
        "artifactBaseImage": "reg.foneshare.cn/base/fs-artifact-repo:v2.0",
        "artifactProject": "artifact"
      },
      "postgres": {
        "host": "*************",
        "port": 5432,
        "user": "fs_pgdb_a_u_k8sapp",
        "password": "HwS#IMLu3U)<*$K={E|D",
        "database": "fs_k8s_app_manager"
      },
      "cas": {
        "loginPath": "http://oss.foneshare.cn/cas/login",
        "logoutPath": "http://oss.foneshare.cn/cas/logout",
        "validatePath": "http://oss.foneshare.cn/cas/p3/serviceValidate"
      },
      "qiXin": {
        "host": "http://restmq.nsvc.foneshare.cn",
        "publishAppId": "publish",
        "importantAlertAppId": "FSAID_131809b",
        "receiverEI": [
          3645,
          5548
        ]
      },
      "sms": {
        "host": "http://************:13526/egress-api-service"
      },
      "fsPaas": {
        "host": "http://************:30393",
        "objectId": "object_op_deployRec__c",
        "enterpriseId": 1,
        "sysUserId": -10000,
        "qiXinSessionFieldId": "5e845a05da7f1c0001ae82ac"
      },
      "k8sAppManager": {
        "host": "https://k8s-app.foneshare.cn"
      },
      "war": {
        "host": "https://war.foneshare.cn",
        "username": "jviCkGFDrOszjKri",
        "password": "ECvpVYQ56cm1MivKVJSPkA"
      },
      "eolinker": {
        "enable": true,
        "host": "http://eolinker.foneshare.cn",
        "eoSecretKey": "mcb1MkL1821ccf52f63fe1ec701f30c14329a54a2a85ab0",
        "spaceIdDefault": "Ns7aIIHd8ab5307649b503da006ccd3a1ec58709b7090a2",
        "reportHost": "https://oss.foneshare.cn/qat"
      },
      "es": {
        "hosts": [
          "http://************:9200",
          "http://************:9200"
        ],
        "index": "k8s-app-pipeline",
        "username": "admin",
        "password": "JynMltlRQteUyACQ"
      },
      "redis": {
        "addr": "{{DEPLOY_APP_NAME}}-redis:6379",
        "password": "",
        "db": 0
      },
      "kafka": {
        "addr": [
            "************:9092",
            "************:9092",
            "************:9092",
            "************:9092",
            "************:9092",
            "************:9092"
        ]
      },
      "clickhouse": {
        "addr": [
          "************:9000",
          "************:9000",
          "************:9000",
          "************:9000"
        ],
        "user": "guest",
        "password": "guest@FA3QYh"
      },
      "openApiTokens": [
        "function__B2rvuWCLtQSOUEao"
      ]
    }
  oncall.json: |-
    {
      "enable": true,
      "scaleUp": {
        "minCPUAvailableCore": 20,
        "minMemoryAvailableGB": 50
      },
      "alertList": [
        {
          "name": "pod-restart-oom-webhook",
          "desc": "OOM次数-自愈-自动扩容",
          "appList": {
            "whitelist": [
              "*/*/*"
            ],
            "blacklist": []
          }
        },
        {
          "name": "jvm-gc-more",
          "desc": "GC时长-自愈-自动扩容",
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "jvm-old-gc-more",
          "desc": "GC次数-自愈-自动扩容",
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "log-error-webhook",
          "desc": "服务错误-自愈-自动扩容",
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "log-apibus-error-service-webhook",
          "desc": "服务提供者apibus错误-自愈-自动扩容",
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "log-cep-error-service-webhook",
          "desc": "服务cep错误-自愈-自动扩容",
          "appList": {
            "whitelist": [
              "*/*/*"
            ],
            "blacklist": []
          }
        },
        {
          "name": "pod-liveness-failed-webhook",
          "desc": "存活探针失败-自愈-自动扩容",
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "jvm-gc-more",
          "desc": "GC耗时-自愈-自动扩容",
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "jvm-old-gc-more",
          "desc": "GC次数-自愈-自动扩容",
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "k8s-node-mem-usage-webhook",
          "desc": "宿主机内存使用率-自愈-驱逐",
          "appList": {
            "whitelist": [],
            "blacklist": []
          },
          "nodeList": {
            "whitelist": [
              "k8s0/*",
              "k8s1/*"
            ],
            "blacklist": []
          }
        },
        {
          "name": "node-load1-k8s-webhook",
          "desc": "宿主机CPU负载-自愈-驱逐",
          "appList": {
            "whitelist": [],
            "blacklist": []
          },
          "nodeList": {
            "whitelist": [
              "k8s0/*",
              "k8s1/*"
            ],
            "blacklist": []
          }
        },
        {
          "name": "app-available-replicas3-webhook",
          "desc": "服务可用率-自愈-自动扩容",
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "jvm-tomcat-http-blocked",
          "desc": "Tomcat的http请求排队-自愈-自动扩容",
          "appList": {
            "whitelist": [
              "*/*/*"
            ],
            "blacklist": []
          }
        },
        {
          "name": "k8s-event-pod-unhealthy",
          "desc": "健康检测失败-自愈-自动扩容",
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "pod-cpu-throttled",
          "desc": "CPU受限-自愈-自动扩容",
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "jvm-thread-blocked",
          "desc": "Pod-JVM线程阻塞",
          "appList": {
            "whitelist": [
              "*/*/*"
            ],
            "blacklist": []
          }
        }
      ]
    }
  settings.json: |-
    {
      "maintain": {
        "ci": {
          "open": false,
          "desc": "为确保大版本发布稳定，2025-05-24 23:00 至 2025-05-25 02:00 期间，不允许执行镜像构建。如确有镜像构建需求，请点击页面上的【紧急构建】按钮，然后根据提示进行操作。"
        },
        "cd": {
          "open": true,
          "desc": "年中封版，截止时间到 2025-07-02 23:00。如需紧急发布，请走紧急上线流程，请各位技术Leader们把好关。“
        }
      },
      "timeWindow": {
        "open": true,
        "excludeNamespaces": [
          "jacoco",
          "ucd-public-test",
          "cloudmodel-public-prod",
          "forceecrm-public-prod",
          "allink8s-public-prod",
          "teleagi-public-prod",
          "forsharecrm-public-prod",
          "tbea-public-prod"
        ]
      },
      "envConfirmText": "线上环境",
      "maxSurgeForceFull": false,
      "eolinkerTestDefault": false,
      "ingressReloadAllowInDay": true,
      "helmChart": {
        "versionPrefix": "9.50."
      },
      "mavenImages": [
        "reg.foneshare.cn/base/fs-maven3.9:openjdk8",
        "reg.foneshare.cn/base/fs-maven3.9:openjdk11",
        "reg.foneshare.cn/base/fs-maven3.9:openjdk17",
        "reg.foneshare.cn/base/fs-maven3.9:openjdk21",
        "reg.foneshare.cn/base/fs-maven3.9:openjdk23",
        "reg.foneshare.cn/base/fs-maven3.9:openjdk24",
        "reg.foneshare.cn/base/fs-maven3.9:dragonwell8",
        "reg.foneshare.cn/base/fs-maven3.9:dragonwell11",
        "reg.foneshare.cn/base/fs-maven3.9:dragonwell17"
      ],
      "clusters": [
        {
          "name": "k8s1",
          "description": "纷享云",
          "version": "1.13",
          "nodeVIP": "************",
          "podIPPrefix": "10.120.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "k8s1.foneshare.cn",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": true,
          "showIngressAddr": true,
          "scaleMaxReplicas": 30,
          "cloudCategory": "fxiaokeCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "",
          "labels": [],
          "namespaces": [
            "sandbox",
            "jacoco",
            "foneshare",
            "foneshare01",
            "foneshare02",
            "foneshare03",
            "foneshare04",
            "foneshare-gray",
            "foneshare-vip",
            "foneshare-svip",
            "foneshare-yinlu",
            "foneshare-haoliyou",
            "foneshare-yqsl",
            "foneshare-ruijie",
            "foneshare-didi",
            "foneshare-stage",
            "foneshare-stage01",
            "foneshare-stage02",
            "foneshare-compatible",
            "foneshare-urgent"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-web-shell:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "Common",
              "remark": ""
            },
            {
              "name": "平台管理系应用",
              "value": "Manage",
              "remark": ""
            },
            {
              "name": "CRM专用",
              "value": "CRM",
              "remark": ""
            },
            {
              "name": "PAAS专用",
              "value": "PAAS",
              "remark": ""
            },
            {
              "name": "自定义对象专用",
              "value": "CustomObject",
              "remark": ""
            },
            {
              "name": "任务类专用",
              "value": "RunTask",
              "remark": ""
            },
            {
              "name": "BI专用",
              "value": "BI",
              "remark": ""
            },
            {
              "name": "快消专用",
              "value": "KuaiXiao",
              "remark": ""
            },
            {
              "name": "企信专用",
              "value": "QiXin",
              "remark": ""
            },
            {
              "name": "DB直连",
              "value": "DB",
              "remark": ""
            },
            {
              "name": "Cep专用",
              "value": "Cep",
              "remark": ""
            },
            {
              "name": "文档预览专用",
              "value": "DocumentPreview",
              "remark": ""
            },
            {
              "name": "社交专用",
              "value": "Social",
              "remark": ""
            },
            {
              "name": "深研专用",
              "value": "ShenYan",
              "remark": ""
            },
            {
              "name": "外网访问权限专用",
              "value": "EgressNode",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": false,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": "https://skywalking.foneshare.cn"
          },
          "thirdServices": {
            "oomReportUrl": "https://k8s-app.foneshare.cn/api/pod/event/report",
            "dubboHelperUrl": "http://************:13367/fs-dubbo-helper",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "foneshare-prometheus",
              "clickHouseDS": "foneshare-clickHouse"
            },
            "clickVisual": {
              "host": "https://log.foneshare.cn"
            }
          }
        },
        {
          "name": "k8s0",
          "description": "纷享云",
          "version": "1.25",
          "nodeVIP": "************",
          "podIPPrefix": "10.128.",
          "ingressTmpl": "ingress-higress.yaml.tmpl",
          "ingressParentHost": "k8s0.foneshare.cn",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": true,
          "showIngressAddr": true,
          "scaleMaxReplicas": 20,
          "cloudCategory": "fxiaokeCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "",
          "labels": [],
          "namespaces": [
            "sandbox",
            "jacoco",
            "foneshare",
            "foneshare01",
            "foneshare02",
            "foneshare03",
            "foneshare04",
            "foneshare-gray",
            "foneshare-vip",
            "foneshare-svip",
            "foneshare-yinlu",
            "foneshare-haoliyou",
            "foneshare-yqsl",
            "foneshare-ruijie",
            "foneshare-didi",
            "foneshare-meifu",
            "foneshare-stage",
            "foneshare-stage01",
            "foneshare-stage02",
            "foneshare-compatible",
            "foneshare-urgent"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-web-shell:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "Common",
              "remark": ""
            },
            {
              "name": "平台管理系应用",
              "value": "Manage",
              "remark": ""
            },
            {
              "name": "CRM专用",
              "value": "CRM",
              "remark": ""
            },
            {
              "name": "PAAS专用",
              "value": "PAAS",
              "remark": ""
            },
            {
              "name": "自定义对象专用",
              "value": "CustomObject",
              "remark": ""
            },
            {
              "name": "任务类专用",
              "value": "RunTask",
              "remark": ""
            },
            {
              "name": "BI专用",
              "value": "BI",
              "remark": ""
            },
            {
              "name": "快消专用",
              "value": "KuaiXiao",
              "remark": ""
            },
            {
              "name": "企信专用",
              "value": "QiXin",
              "remark": ""
            },
            {
              "name": "DB直连",
              "value": "DB",
              "remark": ""
            },
            {
              "name": "Cep专用",
              "value": "Cep",
              "remark": ""
            },
            {
              "name": "文档预览专用",
              "value": "DocumentPreview",
              "remark": ""
            },
            {
              "name": "社交专用",
              "value": "Social",
              "remark": ""
            },
            {
              "name": "函数服务专用",
              "value": "Function",
              "remark": ""
            },
            {
              "name": "深研专用",
              "value": "ShenYan",
              "remark": ""
            },
            {
              "name": "美孚租户专用",
              "value": "TenantMeiFu",
              "remark": ""
            },
            {
              "name": "外网访问权限专用",
              "value": "EgressNode",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": false,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "prometheusMonitor": {
            "enable": true,
            "prometheus": "eye2",
            "metricsInterval": "30s",
            "scrapeTimeout": "5s",
            "metricsPath": "/actuator/prometheus"
          },
          "thirdServices": {
            "oomReportUrl": "https://k8s-app.foneshare.cn/api/pod/event/report",
            "dubboHelperUrl": "http://************:13367/fs-dubbo-helper",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "foneshare-k8s0-prometheus",
              "clickHouseDS": "foneshare-k8s0-clickHouse"
            },
            "clickVisual": {
              "host": "https://log.foneshare.cn"
            }
          }
        },
        {
          "name": "k8s2",
          "description": "纷享云-华为云北京四",
          "version": "1.17",
          "nodeVIP": "************",
          "podIPPrefix": "10.60.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "publicCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "huaweicloud-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "hwc-prometheus",
              "clickHouseDS": "hwc-clickHouse"
            },
            "clickVisual": {
              "host": "https://hwc-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "ale-k8s1",
          "description": "纷享云-阿里云杭州",
          "version": "1.18",
          "nodeVIP": "***********",
          "podIPPrefix": "10.58.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "publicCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "ale-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            },
             {
              "name": "外网访问权限专用",
              "value": "EgressNode",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": "http://ale-prod-skywalking.foneshare.cn"
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "ale-prometheus",
              "clickHouseDS": "ale-clickHouse"
            },
            "clickVisual": {
              "host": "https://ale-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "hws-k8s1",
          "description": "纷享云-亚马逊欧洲法兰克福",
          "version": "1.18",
          "nodeVIP": "************",
          "podIPPrefix": "10.162.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "publicCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "hws-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "hws-prometheus",
              "clickHouseDS": "hws-clickHouse"
            },
            "clickVisual": {
              "host": "https://hws-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "ksc-k8s1",
          "description": "纷享云-亚马逊东亚香港",
          "version": "1.29",
          "nodeVIP": "************",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "publicCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "ksc-ksc-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "apm": {
            "enable": false,
            "skyWalkingUI": "http://ksc-prod-skywalking.foneshare.cn"
          },
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": false,
            "max": 2
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "ksc-prometheus",
              "clickHouseDS": "ksc-clickHouse"
            },
            "clickVisual": {
              "host": "https://ksc-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "forsharecrm-k8s1",
          "description": "纷享云-亚马逊东南亚新加坡",
          "version": "1.29",
          "nodeVIP": "***********",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "publicCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "forsharecrm-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "forsharecrm-prometheus",
              "clickHouseDS": "forsharecrm-clickHouse"
            },
            "clickVisual": {
              "host": "https://forsharecrm-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "kemaicrm-k8s1",
          "description": "纷享云-亚马逊北美北加州",
          "version": "1.29",
          "nodeVIP": "***********",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "publicCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "kemaicrm-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "kemaicrm-prometheus",
              "clickHouseDS": "kemaicrm-clickHouse"
            },
            "clickVisual": {
              "host": "https://kemaicrm-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "sbt-k8s1",
          "description": "应用专属-双胞胎",
          "version": "1.17",
          "nodeVIP": "************",
          "podIPPrefix": "10.202.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "dedicatedCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "huaweicloud-sbt-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": false,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "sbt-prometheus",
              "clickHouseDS": "sbt-clickHouse"
            },
            "clickVisual": {
              "host": "https://sbt-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "ucd-k8s2",
          "description": "应用专属-紫光云",
          "version": "1.17",
          "nodeVIP": "************",
          "podIPPrefix": "10.193.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "hybirdCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "ucd-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": false,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "ucd-prometheus",
              "clickHouseDS": "ucd-clickHouse"
            },
            "clickVisual": {
              "host": "https://ucd-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "xjgc-k8s1",
          "description": "应用专属-许继",
          "version": "1.18",
          "nodeVIP": "***********",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "hybirdCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "xjgc-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "xjgc-prometheus",
              "clickHouseDS": "xjgc-clickHouse"
            },
            "clickVisual": {
              "host": "https://xjgc-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "hisense-k8s1",
          "description": "应用专属-海信",
          "version": "1.18",
          "nodeVIP": "***********",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "hybirdCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "hisense-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "hisense-prometheus",
              "clickHouseDS": "hisense-clickHouse"
            },
            "clickVisual": {
              "host": "https://hisense-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "chinatower-k8s1",
          "description": "应用专属-铁塔",
          "version": "1.18",
          "nodeVIP": "***********",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "dedicatedCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "chinatower-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "chinatower-prometheus",
              "clickHouseDS": "chinatower-clickHouse"
            },
            "clickVisual": {
              "host": "https://chinatower-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "mengniu-k8s1",
          "description": "应用专属-蒙牛",
          "version": "1.25",
          "nodeVIP": "*************",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 20,
          "cloudCategory": "dedicatedCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "mengniu-public-prod",
            "mengniu-public-prod-01",
            "mengniu-public-prod-02",
            "mengniu-public-prod-03"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            },
            {
              "name": "多语服务专用",
              "value": "I18NService",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "mengniu-prometheus",
              "clickHouseDS": "mengniu-clickHouse"
            },
            "clickVisual": {
              "host": "https://mengniu-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "hsyk-k8s1",
          "description": "应用专属-何氏眼科",
          "version": "1.25",
          "nodeVIP": "***********",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "hybirdCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "hsyk-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "hsyk-prometheus",
              "clickHouseDS": "hsyk-clickHouse"
            },
            "clickVisual": {
              "host": "https://hsyk-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "wuzizui99-k8s1",
          "description": "应用专属-伍子醉",
          "version": "1.25",
          "nodeVIP": "**********",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "dedicatedCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "wuzizui99-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "wuzizui99-prometheus",
              "clickHouseDS": "wuzizui99-clickHouse"
            },
            "clickVisual": {
              "host": "https://wuzizui99-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "iflytek-k8s1",
          "description": "应用专属-科大讯飞",
          "version": "1.25",
          "nodeVIP": "***********",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "hybirdCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "iflytek-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "iflytek-prometheus",
              "clickHouseDS": "iflytek-clickHouse"
            },
            "clickVisual": {
              "host": "https://iflytek-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "hexagonmi-k8s1",
          "description": "应用专属-海克斯康",
          "version": "1.25",
          "nodeVIP": "**********",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "dedicatedCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "hexagonmi-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "hexagonmi-prometheus",
              "clickHouseDS": "hexagonmi-clickHouse"
            },
            "clickVisual": {
              "host": "https://hexagonmi-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "yangnongchem-k8s1",
          "description": "应用专属-扬农化工",
          "version": "1.25",
          "nodeVIP": "**************",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "hybirdCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "yangnongchem-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "yangnongchem-prometheus",
              "clickHouseDS": "yangnongchem-clickHouse"
            },
            "clickVisual": {
              "host": "https://yangnongchem-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "teleagi-k8s1",
          "description": "应用专属-电信智控",
          "version": "1.25",
          "nodeVIP": "***********",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "hybirdCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "teleagi-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "teleagi-prometheus",
              "clickHouseDS": "teleagi-clickHouse"
            },
            "clickVisual": {
              "host": "https://teleagi-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "cpgc-k8s1",
          "description": "应用专属-中船宁夏",
          "version": "1.25",
          "nodeVIP": "",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "dedicatedCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "cpgc-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "cpgc-prometheus",
              "clickHouseDS": "cpgc-clickHouse"
            },
            "clickVisual": {
              "host": "https://cpgc-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "wingd-k8s1",
          "description": "应用专属-中船法兰克福",
          "version": "1.25",
          "nodeVIP": "",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "dedicatedCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "wingd-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "wingd-prometheus",
              "clickHouseDS": "wingd-clickHouse"
            },
            "clickVisual": {
              "host": "https://wingd-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "kehua-k8s1",
          "description": "应用专属-科华",
          "version": "1.25",
          "nodeVIP": "",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "hybirdCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [
          "专线关闭中，有问题科华群反馈"
          ],
          "namespaces": [
            "kehua-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "kehua-prometheus",
              "clickHouseDS": "kehua-clickHouse"
            },
            "clickVisual": {
              "host": "https://kehua-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "jingbo-k8s1",
          "description": "应用专属-京博",
          "version": "1.32",
          "nodeVIP": "",
          "podIPPrefix": "172.19.",
          "ingressTmpl": "",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "hybirdCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "jingbo-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "jingbo-prometheus",
              "clickHouseDS": "jingbo-clickHouse"
            },
            "clickVisual": {
              "host": "https://jingbo-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "tbea-k8s1",
          "description": "应用专属-特变",
          "version": "1.29",
          "nodeVIP": "",
          "podIPPrefix": "10.233.",
          "ingressTmpl": "",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "hybirdCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "tbea-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "tbea-prometheus",
              "clickHouseDS": "tbea-clickHouse"
            },
            "clickVisual": {
              "host": "https://tbea-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "cloudmodel-k8s1",
          "description": "其它环境-IDC乌兰察布-模板云",
          "version": "1.29",
          "nodeVIP": "***********",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "publicCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "cloudmodel-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "cloudmodel-prometheus",
              "clickHouseDS": "cloudmodel-clickHouse"
            },
            "clickVisual": {
              "host": "https://cloudmodel-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "forceecrm-k8s1",
          "description": "其它环境-IDC乌兰察布-私有云",
          "version": "1.29",
          "nodeVIP": "",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "publicCloud",
          "opsDeploy": true,
          "schedulerName": "",
          "imageRegistryProxy": "",
          "labels": [
            "该环境发布请联系@陈威 @何金富"
          ],
          "namespaces": [
            "forceecrm-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "forceecrm-prometheus",
              "clickHouseDS": "forceecrm-clickHouse"
            },
            "clickVisual": {
              "host": "https://forceecrm-prod-log.foneshare.cn"
            }
          }
        },
        {
          "name": "ucd-k8s1",
          "description": "其它环境-IDC乌兰察布-性能云",
          "version": "1.29",
          "nodeVIP": "**********",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": false,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "publicCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "ucd-public-test",
            "ucd-public-test-01",
            "ucd-public-test-02",
            "ucd-public-test-03"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8",
            "reg.foneshare.cn/base/fs-tong-web7:openjdk8",
            "reg.foneshare.cn/base/fs-tong-web7:openjdk21",
            "reg.foneshare.cn/base/fs-tong-web7:ali-dragonwell8",
            "reg.foneshare.cn/base/fs-tong-web7-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tong-web7-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tong-web7-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tong-web7-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tong-web7-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tong-web7-ffmpeg3.3.4:openjdk8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": false,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "ucd-test-prometheus",
              "clickHouseDS": "ucd-test-clickHouse"
            },
            "clickVisual": {
              "host": "https://ucd-test-log.foneshare.cn"
            }
          }
        },
        {
          "name": "allink8s-k8s1",
          "description": "其它环境-IDC乌兰察布-ALLINK8S",
          "version": "1.25",
          "nodeVIP": "***********",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showNodeVipAddr": false,
          "showIngressAddr": false,
          "scaleMaxReplicas": 5,
          "cloudCategory": "publicCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "imageRegistryProxy": "apdb-harbor.foneshare.cn:30040/foneshare-proxy",
          "labels": [],
          "namespaces": [
            "allink8s-public-prod"
          ],
          "baseImages": [
            "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk23",
            "reg.foneshare.cn/base/fs-tomcat10:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.foneshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat9-ffmpeg3.3.4:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-ffmpeg3.4.2:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk17",
            "reg.foneshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.foneshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 2
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": ""
          },
          "thirdServices": {
            "oomReportUrl": "",
            "dubboHelperUrl": "",
            "grafana": {
              "host": "https://grafana.foneshare.cn",
              "prometheusDS": "allink8s-prometheus",
              "clickHouseDS": "allink8s-clickHouse"
            },
            "clickVisual": {
              "host": "https://allink8s-prod-log.foneshare.cn"
            }
          }
        }
      ],
      "thirdServices": {
        "webShellHost": "https://oss.foneshare.cn/fs-k8s-web-shell",
        "devopsEventUrl": "https://grafana.foneshare.cn/d/a10c8008-2deb-4ea5-863b-7c9d5c1c1f14/e4ba8b-e4bbb6-e4b8ad-e5bf83"
      },
      "appSuffix": {
        "foneshare-gray": "-gray",
        "jacoco": "-jacoco",
        "sandbox": "-sandbox",
        "foneshare-vip": "-vip",
        "foneshare-svip": "-svip",
        "foneshare-yinlu": "-yinlu",
        "foneshare-haoliyou": "-haoliyou",
        "foneshare-yqsl": "-yqsl",
        "foneshare-ruijie": "-ruijie",
        "foneshare-didi": "-didi",
        "foneshare-meifu": "-meifu",
        "foneshare-compatible": "-compatible",
        "foneshare-stage": "-stage",
        "foneshare-stage01": "-stage01",
        "foneshare-stage02": "-stage02",
        "foneshare-urgent": "-urgent",
        "huaweicloud-public-prod": "-huawei",
        "ucd-public-test": "-ucd-test",
        "ucd-public-prod": "-ucd-prod",
        "huaweicloud-sbt-prod": "-sbt-prod",
        "ksc-ksc-prod": "-ksc",
        "hws-public-prod": "-hws-prod",
        "ale-public-prod": "-ale",
        "cloudmodel-public-prod": "--cloudmodel",
        "forceecrm-public-prod": "--forceecrm",
        "hisense-public-prod": "--hisense",
        "xjgc-public-prod": "--xjgc",
        "mengniu-public-prod": "--mengniu",
        "hsyk-public-prod": "--hsyk",
        "wuzizui99-public-prod": "--wuzizui99",
        "yangnongchem-public-prod": "--yangnongchem",
        "iflytek-public-prod": "--iflytek",
        "kemaicrm-public-prod": "--kemaicrm",
        "allink8s-public-prod": "--allink8s",
        "forsharecrm-public-prod": "--forsharecrm",
        "teleagi-public-prod": "--teleagi",
        "cpgc-public-prod": "--cpgc",
        "wingd-public-prod": "--wingd",
        "kehua-public-prod": "--kehua",
        "jingbo-public-prod": "--jingbo",
        "tbea-public-prod": "--tbea",
        "chinatower-public-prod": "--chinatower",
        "hexagonmi-public-prod": "--hexagonmi"
      },
      "deployStrategies": [
        {
          "name": "滚动",
          "value": "ROLL_UPDATE"
        },
        {
          "name": "重建",
          "value": "RECREATE"
        }
      ],
      "parentPoms": [
        {
          "name": "候选版 (RC)",
          "value": "fxiaoke-parent-pom-rc",
          "desc": "通过了线下环境测试或者一些紧急Bug修复的依赖包版本",
          "enable": true
        },
        {
          "name": "稳定版 (Release)",
          "value": "fxiaoke-parent-pom-release",
          "desc": "在候选版通过充分测试后，需要进入到全网的依赖包版本",
          "enable": true
        },
        {
          "name": "复制云专用(forceecrm-rc)",
          "value": "fxiaoke-parent-pom-forceecrm-rc",
          "desc": "只限于复制云环境使用",
          "enable": true
        },
        {
          "name": "测试版 (Alpha)",
          "value": "fxiaoke-parent-pom-alpha",
          "desc": "包含需要进行测试的依赖包，只在线下环境或线上Jacoco可用",
          "enable": true
        }
      ],
      "oncallScaleUp": {
        "appList": [
          "*/*/fs-k8s-tomcat-test",
          "*/*/checkins-office-v2-server",
          "*/*/data-auth-service",
          "*/*/fs-apibus-global",
          "*/*/fs-apibus-ncrm",
          "*/*/fs-apibus-paas",
          "*/*/fs-appserver-checkins-v2",
          "*/*/fs-bi-crm-report-web",
          "*/*/fs-bi-sqlengine",
          "*/*/fs-bi-stat",
          "*/*/fs-bi-udf-report",
          "*/*/fs-bi-uitype",
          "*/*/fs-bpm",
          "*/*/fs-bpm-after-action",
          "*/*/fs-checkins-biz",
          "*/*/fs-crm",
          "*/*/fs-crm-fmcg-service",
          "*/*/fs-crm-fmcg-wq",
          "*/*/fs-crm-manufacturing",
          "*/*/fs-crm-sfa",
          "*/*/fs-crm-workflow",
          "*/*/fs-feeds-provider",
          "*/*/fs-flow",
          "*/*/fs-fmcg-sales-cgi",
          "*/*/fs-metadata-option",
          "*/*/fs-metadata-rest",
          "*/*/fs-open-app-center-provider",
          "*/*/fs-organization-adapter",
          "*/*/fs-organization-biz",
          "*/*/fs-organization-provider",
          "*/*/fs-organization-provider-4data-auth",
          "*/*/fs-paas-app-udobj",
          "*/*/fs-paas-app-udobj-rest",
          "*/*/fs-paas-app-udobj-rest4flow",
          "*/*/fs-paas-app-udobj-rest4realtime",
          "*/*/fs-paas-auth-provider",
          "*/*/fs-paas-bizconf-web",
          "*/*/fs-paas-function-service-debug",
          "*/*/fs-paas-function-service-runtime",
          "*/*/fs-paas-function-service-runtime-provider",
          "*/*/fs-paas-org",
          "*/*/fs-paas-rule",
          "*/*/fs-paas-workflow",
          "*/*/fs-plat-auth-biz",
          "*/*/fs-plat-org-management",
          "*/*/fs-stage-propeller",
          "*/*/fs-stone-dataserver",
          "*/*/fs-stone-fileserver",
          "*/*/fs-stone-metaserver",
          "*/*/fs-stone-proxy",
          "*/*/fs-user-extension-biz",
          "*/*/fs-webpage-customer-provider",
          "*/*/fs-bi-goal-web",
          "*/*/fs-bi-custom-statistic-offline",
          "*/*/fs-cep-provider",
          "*/*/i18n-setting",
          "*/*/open-api-gateway-web",
          "*/*/fs-bi-industry",
          "*/*/fs-hubble-query",
          "*/*/fs-paas-license",
          "*/*/fs-paas-web",
          "*/*/fs-plat-app-view-cgi",
          "*/*/fs-plat-login-cloud"
        ]
      },
      "baseImageGray": {
        "imageTagSuffix": "-v250527",
        "appList": [
          "forceecrm-k8s1/*/*",
          "ucd-k8s1/*/*",
          "cloudmodel-k8s1/*/*",
          "allink8s-k8s1/*/*",
          "*/foneshare-gray/*",
          "*/*/fs-k8s-tomcat-test",
          "*/*/egress-api-gateway",
          "*/*/fs-egress-api-notify-push",
          "*/*/egress-proxy-service",
          "*/*/fs-eye-consumer",
          "*/*/fs-devops-console"
        ]
      },
      "pipelineDefault": {
        "status": "audit",
        "cluster": "k8s0",
        "namespace": "foneshare",
        "baseImage": "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
        "replicas": 2,
        "deployStrategy": "ROLL_UPDATE",
        "resources": {
          "requestCPU": 0.2,
          "requestMemory": 512,
          "limitCPU": 0.5,
          "limitMemory": 1024
        },
        "livenessProbe": {
          "enable": true,
          "initialDelaySeconds": 1800,
          "periodSeconds": 30,
          "timeoutSeconds": 5,
          "failureThreshold": 4,
          "successThreshold": 1
        },
        "readinessProbe": {
          "enable": true,
          "initialDelaySeconds": 10,
          "periodSeconds": 10,
          "timeoutSeconds": 5,
          "failureThreshold": 3,
          "successThreshold": 1
        },
        "startupProbe": {
          "enable": false,
          "initialDelaySeconds": 10,
          "periodSeconds": 10,
          "timeoutSeconds": 5,
          "failureThreshold": 180,
          "successThreshold": 1
        },
        "schedule": {
          "strategy": "PREFERRED",
          "node": ""
        },
        "pvc": {
          "enable": false,
          "name": "",
          "mountPath": ""
        },
        "envs": [
          {
            "name": "K8S_APP_NAME",
            "value": "[系统赋值]",
            "type": "SYSTEM"
          },
          {
            "name": "K8S_PROCESS_NAME",
            "value": "[系统赋值]",
            "type": "SYSTEM"
          },
          {
            "name": "ENVIRONMENT_TYPE",
            "value": "foneshare",
            "type": "SYSTEM"
          },
          {
            "name": "MACHINE_TYPE",
            "value": "DOCKER",
            "type": "SYSTEM"
          },
          {
            "name": "CMS_ENV_TYPE",
            "value": "foneshare",
            "type": "SYSTEM"
          },
          {
            "name": "CATALINA_OPTS",
            "value": "[系统赋值]",
            "type": "SYSTEM"
          },
          {
            "name": "JAVA_OPTS",
            "value": "[系统赋值]",
            "type": "SYSTEM"
          }
        ],
        "ports": [
          {
            "name": "http",
            "value": 80,
            "type": "SYSTEM"
          }
        ],
        "eolinkerIDs": [],
        "webhook": {
          "url": ""
        },
        "options": {
          "isCoreApp": false,
          "onlyDeployTag": true,
          "addSysctlKeepalive": false,
          "skyWalkingAgent": false,
          "appLogToKafka": true,
          "buildUseRuntimeJDK": false,
          "jvmGcLog": true
        },
        "partnerApps": [],
        "exclusiveApps": [],
        "preStopWebhook": "",
        "preStopRetainSeconds": 20,
        "remark": ""
      },
      "checkVersionOfDedicatedCloud": [
        {
          "cluster": "cloudmodel-k8s1",
          "namespace": "cloudmodel-public-prod"
        },
        {
          "cluster": "xjgc-k8s1",
          "namespace": "xjgc-public-prod"
        },
        {
          "cluster": "hisense-k8s1",
          "namespace": "hisense-public-prod"
        },
        {
          "cluster": "forceecrm-k8s1",
          "namespace": "forceecrm-public-prod"
        },
        {
          "cluster": "chinatower-k8s1",
          "namespace": "chinatower-public-prod"
        }
      ]
    }
  k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUN3akNDQWFxZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFTTVJBd0RnWURWUVFERXdkcmRXSmwKTFdOaE1CNFhEVEU1TURZeU56QTNOREV6TmxvWERUSTVNRFl5TkRBM05ERXpObG93RWpFUU1BNEdBMVVFQXhNSAphM1ZpWlMxallUQ0NBU0l3RFFZSktvWklodmNOQVFFQkJRQURnZ0VQQURDQ0FRb0NnZ0VCQU9PS3gwbUFhKzlBCm5zbklmZWc4c2lVUFhsbmoxQ2xLenpQSWJGNGt3d3g4QytSOHphYlNsTEtrT3V4K2tQMUgzd0tURW14emVtY0QKSElrSEJUcTZubE5kOU1qMW9wTVBKNVhsU09IMFpwYTlYWThFUjcwU1c4VlVTa2ZpSVNjd0tlWWxLYU9KVWFWdQpNd29aU0lZdkVzZU1KemIyR25hN2ppd05Vc3lSZ2J0MUxJWnpNSzlRSVNFRUluTmNrTW13bGc4bFh3WlBtYXpnCmdzaVpLUkg4Y3RxOHRDbStIZ0FSeHVPeGtrUDZzcitRTVh5ZC9vRXNxVFB2bVVDQ3VJZDAzWEc4eWp2bmZ3NmgKRVRVRGhXNnBwT0VCYVR3MmU1TzNIVlJLazFCM3l4d0ZiZDRIU1JGaWN2TmhxZGFnYnpMbllOVG1CTFFvc2JKSgphZTFVMEw4Zmg4MENBd0VBQWFNak1DRXdEZ1lEVlIwUEFRSC9CQVFEQWdLa01BOEdBMVVkRXdFQi93UUZNQU1CCkFmOHdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSVZRWVFqQVRCZjNkZTJwT2VNRkRYWHNYditxVXFYVmZpeXoKQlA5SHlBdkcwYUk1cWJINUNHVGoyUXd5OEZ0ZzRNUGtsdFNvZGtETWRyYmlNQkYrZG1TanlXRWdmcTRUTUpIOApIdzJ2VlArUlBGSG1HTkVXZ0xkM250aGlXU2xVMWw3YkNveEp5dVF2SCs4TGVjVlF1YWgwWGlyb3RVRUdzV3BhCm5RYk45R1M3cWwzYVc5NHpyRFR2UkE2dWNDbHoxVXhSdUsxU3dzOWc1QytneURDYnRZYjljZStHZGtuMFJRSkYKN0hQUlpxRWVET3lqNng3V0tLcFlZbmE5dEhhQTNHVEJhNmpjVG80WEJQakQwTUFDWUt4ZnRmdlZxUDZXM3lKVApHVXp0NWczdmxBWms4aEpkRGthdHNNN1hWTXg3U1dwTTJiMzRseXFiUnYxNGpTQjgxeUU9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          server: "https://k8s1.foneshare.cn:6443"
    contexts:
      - name: fs-k8s-app-manager@k8s1
        context:
          cluster: k8s1
          namespace: default
          user: fs-k8s-app-manager@k8s1@user
    users:
      - name: fs-k8s-app-manager@k8s1@user
        user:
          token: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@k8s1
  k8s0: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: foneshare-k8s0
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJek1ETXhPREUyTWpFMU5Gb1hEVE16TURNeE5URTJNakUxTkZvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTmJKCk1vUHYzajFudDVIdTUyY0ZhOHRpWGNqTU1LQjdlWU1PRGU3d1M0amlkOGhNY3Rxd0l5UjU1bmxGc2NpOE1qcWEKdk1vTkozaUF4V1o5WnkvVmxJRDNUNTI0RlhTOUJyREZid3Y5Ri9Cb3pOam0zTUtPLzNYeVFNZHJ1M29RQWxQVgp5Z0RRZ096eXkybXBIcXZrY2IyelA0NjcwVVVCckg4d2EvTDhXS1hvUUxhYW13VGFHU3hQay9nOG4yQ0wzekVECjNhYzlTb3pUcFdmbit6OTgra3g5dm5hZXNoanhrc2wxYVB4YlJ0YnpXc0FWeDJsRHhrTEh6bXd3VHRJQVlqUnIKYXBvZnF1aWhqRjFTVW55VTQyTTIxQzV4RzRhMXZVZENZNGlhektqY2o4aHhDZkN2M1ZzbzFDWnZXNGlaNThmeApKemVLdjF0QmJhS1ZjRDhOZS9rQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZGYmQ4NU1KZnZyaEVocXBXbWZBWDhtL29Ec0dNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBQWRKK0ZxeEpjd2NVQllBa09MMwpMVkZzdndVVlZKS2c5VVlQd0tOTi80NnBuWG1iSmJLNzVuU3hYS1IyMWZoaU9NMHJENVdjYnVnd1RtYitWRXJ1CnZyQzNjM3Q1U3dCMmpzMEZ1TXYzY1BNcFlaSXBldElLM2pWVVRvT3EvYmtsdWJKOW03WEo2RXNXNEF4bzNhcDUKQ3dnM1NmdU9WTWpNZU53ZUYzdEtWbkU1eStqU3RuRU1yZXJBZFRKY0IxOGpQQzRDS0JGNEJmMS9OT1hvTnRCWgprbWptL0M1KytWTW9KT05oZ1lqT2t1OTlLQ3dqR3pmcVFJYWxBNU1CSnA3YXkvN2ZKbW9BK0Nva0lQNlBsZXdHCkNPeHM4Q2FjREsyS1prNlFTckRaQzg3eFVrS1M0RzlwRzFBYm1UQlUvZ0FUU0l0OEFnK2I5d1Z2VHp2RGxmN2YKYnljPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          server: https://*************:6443
    contexts:
      - name: fs-k8s-app-manager@foneshare-k8s0
        context:
          cluster: foneshare-k8s0
          namespace: default
          user: fs-k8s-app-manager
    users:
      - name: fs-k8s-app-manager
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@foneshare-k8s0
  k8s2: |-
    apiVersion: v1
    clusters:
      - cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUN5RENDQWJDZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJd01ESXlNekExTlRrME5Gb1hEVE13TURJeU1EQTFOVGswTkZvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTllVCit4VEI3OWJFZzh2MGNJV0xLUmFpSGF5ODVHc1BKUUpQcm5zUFNFZHZGNkRiOUIwbzF6OHpiZGxxL0xoK2pUUEUKNWRkbTh6SjNRQmxZRC8zY3FVNVRrUlV1MDQ2TElRZk5ONVJYd3JGdDRnVXJZcjJJaDFISHgzTGFJOVhlVEZpTgpqZUtqTjN6dDRqNnBVdTZoQkUraWZTcVlRMFBXZlpWVlhGTEVEbThmNWx2OVU5cHlPU241MlU2VGZlUW9KRXJkCjBDRUZMMU5HN3VEMVU2NExWakVEZCt5NysrcnVTZnM4QlYwTmI0a1pIUWF6d2lMN3ZudTgwWjhPMVkyMzUwSzcKNFVsa0c2MWJEblRSakloV2VyU3FPb0FwTUVoUm05elo1cnVZRnFoQWZSZlZIbzFPNUtqSjNXVExoK3ovZDhJNApOZmFpZW9iU1dqcGZlcS9oZjBVQ0F3RUFBYU1qTUNFd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQkFBSkVoTzBUWkNwL2tpSllHVHZ2blpaczA4MlgKbU54OHltSTFxSVg5RmJLLyt3a3NZRXluM0dJSTJad1d2REM5TDJrTnIzRHlBdVJTRkN2Q0ZSUFpieHh1b0F3ZQpsS3JrdCtUYncvQll3OC9zODNLUW9rdEFsSjRTMVE4dXNxN3RCaHRHQ1JyRXVMd2E2YUlCblNrcFZwdVBMZ3hVCjBXcHhLVkhiM2FiTlFKUlhKMGQyZHVnUnlkNEFIVXpRUnIvT1NOeEIwMnFrd0tkakw3bUF6MWZ3TXd4RUZHcUQKZUdvdTBJYWFzeHUwUFNQM2hOaGhnZlhXbVp4ajhzZVhieVBDR2UxM055WkJ5S0tPbW1iRGYzVFVtZDFLenQreQpMYzBGdmdxcXNXeEFuREh5R0p4YmxQU1JTeXZmalNpMXFoOHNrM2tDdmllZUU4aG96c2pBMFJUOWttVT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
          server: https://k8s2.foneshare.cn:6443
        name: kubernetes
    contexts:
      - context:
          cluster: kubernetes
          user: kubernetes-admin
        name: kubernetes-admin@kubernetes
    current-context: kubernetes-admin@kubernetes
    kind: Config
    preferences: { }
    users:
      - name: kubernetes-admin
        user:
          token: **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

  ucd-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: ucdtest-k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJQ2JZaEs1SERDdzR3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBMk1EUXhNakl3TURsYUZ3MHpOVEEyTURJeE1qSTFNRGxhTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUURoNXpqdFVXTkQwTnhDZWFqT0V3WUNldXhuVWRPS0Fub01Ob2NCWTVOY2puYytxV1RENlI4VGcrQjgKTjBGNFYwZ0hDbkFNdC8wakl6Q3dDWndrS3hrNVNQWjUwZHg1MnpjY3FNQkRrNmRNeHFCTk5abHdvSnk5eUw0LwpBOXJYM0d0enBtWUZLOVNFNHlqbGV2dVNXOFdUV2RzYXBGdkc3WE94elFwRFRMV1pwWlRaY1dmNTQ3NFQ4bGtvCnRSWjZTcEVQWHljQVFLcThnOG9ER0prMXJMVGhMMUhVQ1U5a0tSTnExR01LVkt0UlBHUS9WMWtleTEzYktOMk8KNGhNY1VESUtwSkxvM3VEM28rU1I2cDJady9JeW1MdHE3dmo3bk4wenEwL0VZYUh3RTRjdmt3RUdLdy9kdFczTAppMDB0WEJKQXNBSVR5ZDFJNG9VVFRlNXc1ZDZoQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJSWkdPVGcrWWhydS93aXF6aTgvZjUzK0FFZldUQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRRGh4V05TbGNaeQpBNUNGd3RCT3duVEFySldWREhwS2RPV0R0bTJDVlB6VzcvYk5uNEl5eEd0TzN5VW5RYmROeGlzRitwaUhaSlVqCmpvb0czeWFzbHkxZVJ4N0tPQ3VGM2w2VnpZVVk2VmRvUWh0VTg4YTVFaGhmUExjcXhPRHp4MEpIZUUyTW9WMTUKTitRTjBUSEN0SHdsS1NxWTlFN2xvbmpYOGlaOWwrUkQ3Z0dMTGFjWlVFekNKOEF6ZUVMdXkyS2tidjFOMTgyQgpDN0xmVWdUSlgzSFhIVGRRLys2bzhSb3BqUkwxa2p5amtCRGlBNXBWck84aEUrSSt1Q0wyN0x3cmNpMnF6SWc1CmRUQUkwenE1cUhGNFpIUmJQUW5lcVpsWTY5WDJhR3BxbmNhNkZ3dExUYm9qUEhnK01TUVlnNVZnRmN0MHVMNisKS1N2anBJWWViM3lQCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          server: https://ucdtest-k8s1.foneshare.cn:6443
    contexts:
      - name: fs-k8s-app-manager@ucdtest-k8s1
        context:
          cluster: ucdtest-k8s1
          namespace: default
          user: fs-k8s-app-manager@ucdtest-k8s1@user
    users:
      - name: fs-k8s-app-manager@ucdtest-k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@ucdtest-k8s1
  ucd-k8s2: |-
    apiVersion: v1
    clusters:
      - cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUN5RENDQWJDZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJd01EVXlNREE0TVRVek4xb1hEVE13TURVeE9EQTRNVFV6TjFvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTUp5CmVLWFJvVTYxNjdnNHJFcmpMSU81Y0c1YUxnZ3dxcTBqSk9WOFdIbXpkWFFRdGpxdkFPcGw3MEZySG95T2U3OEEKUWcvTFkyektxS2pXUjVadGJJdFhjTUF5RFI2YUtwNE9iUGpjaHBSc3E5Y2xQajBNTnBlL2tMLzJXaGgxNnRzQwpCeFlaWkFrOVBGV2FMNFV1a0dxMCtMa3N1RUxiMGc5MmRIMWxoR3dMV0FHdGJaYktjOFRvaWFMbFd6amZ4VHRsCmZUWUtzQjRuWTRFNThFbnBZeXh3KzE1NVVSbUU5SXJiSlNhOW55U2U4VWI5ZjRQU1VuampBbllxbk5Ybzg1WDgKRVdVY2ZteFRUNUhtT2NJeFd2djNVU2RXaGdPSXdudHJXR2twZ1BrRUd1YzRTQ3loRmx3T3JRRm50ZDNoWlNNbgp0MjBTM2VzZU5NVWUzcmhOK1RVQ0F3RUFBYU1qTUNFd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQkFKcExQTE5VS2NGZ3Z5Zk9tTFpQZkpIY2tnRy8KQTVXNmcrbVJ6dlRIYXI1d25TRTR3UVRPQ2R0NHRZSmtyT05jaTBVUWpSbys3cW9SYUtremU5WU1aeU1vTFNtbgpCaEo3aG1odW9YM1ZLaE9tN01iNnFPK3B3QU9ydDBaV244L2FndXVFMTBSUFVqVTYxL3ZML0ViT0RzR0thQktwCnR4ZEJ3OVE2OEtKcHl3Nk5oR1hKME9wSlFhZHo0aXdMU01uREpmanZoWUhNald5YkZ3WVJRZWcwbnYzK2JRdGsKY1NkajNBcHlvNTlMd1Z5MkNVNm5VeGEzSVJEV3ZmYnY5SUNhU1dCa2YxcFU3L0xtTUZTeTJSZEMyV1N6RldnMQp6cGIzdHY4ZmZFR3pzSWZEV1RoU1o1UEl4anBHcHZOV0hGK0xxY1NyMUVYZnJRYW1nUW5kU2JxZk9TOD0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
          server: https://ucd-k8s2.foneshare.cn:6443
        name: kubernetes
    contexts:
      - context:
          cluster: kubernetes
          user: kubernetes-admin
        name: kubernetes-admin@kubernetes
    current-context: kubernetes-admin@kubernetes
    kind: Config
    preferences: {}
    users:
      - name: kubernetes-admin
        user:
          token: **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

  sbt-k8s1: |-
    apiVersion: v1
    clusters:
      - cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUN5RENDQWJDZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJd01EWXhPVEEyTkRBeE1Gb1hEVE13TURZeE56QTJOREF4TUZvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBSndBClEwaGJ3OFVPbWVnSHZGTFI5bzdZWmpHU0pSQzIweUY3b0x0TFROYVI3eGY3eUtnR2cxQmp2Qi80Q1pUVEZZWW4KQzdMelIvWGdJOUc1alBsbW95NEI0VmpxZ0ZSdUVIMVJkTGwrT3IrTGFXMXA2d241dDFVTDJqcXQ0VE4wZ0hRZQpKSkpVbE93aEROZkh6QWpXNnRlSFd5eTFEd1VnMUM2RE9kbkVKR1dEdzBYdzdHMGgxcWEwZmN1eWt5UDZNRVliCmRDcGZGdTN6aGtGQThWRDNyRTFValZLQWtPa2xZMmhsNGhmZGhHY2Q2UXM2UEVUUzlrWEhpVTdNYVRPUk14RHUKWEQwVXhqOFBVQ05paWgxNjhWYW1EZTNoSVJIOEptT1g5QTZ2NFlGSDZMSC91eVJYUkEwRS9mNy9pSS92KzMzaQorakx0VEVROHBMaTMxWFhaRGprQ0F3RUFBYU1qTUNFd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQkFGTnpGNjlKRjNPMWRMZ3hMT1ZHbWpGTUh4M2kKSmluN2pPNy9US2J0QnRtTW5SUXVvRUxINHhUQ25mc3J2NW42Z1dsaWsxRzl5VmlTT2hXU1B3MlkzanBwSlVObgpPaTV5SkxJZUluV25NVWJiT3RNZEZ1MjRjZmxlZVB4RGpNc3QxcUgrVkplT3U4MXptRlFuT0lGV1BGeDZpL0tpCmNUMXVoTFpyZzBLV1IvblgzbFZVYXZmKzZiZFg3aUFtWlBER2g2cjBTVDY5VGRhSDc4R3hJdng0Z0lSa3hOajIKZzVDTW44QmpEY3NOaTQ3ZUNlUVJRZXluVlJZUHJLM1d2MXNmaDNwVy9IYjZjdnhQT1ExUk5mZTJNMllpWk5xeAozazRWaU5lUjQyd3hndjd0N05iaDdUVjBDTUdpYkF6c2tERGVzSUN0YkpkbnErbjJzMkc2T0ZUdC9OTT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
          server: https://sbt-k8s1.foneshare.cn:6443
        name: kubernetes
    contexts:
      - context:
          cluster: kubernetes
          user: kubernetes-admin
        name: kubernetes-admin@kubernetes
    current-context: kubernetes-admin@kubernetes
    kind: Config
    preferences: {}
    users:
      - name: kubernetes-admin
        user:
          token: **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

  ksc-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: ksc-k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJWkZvRGh1NEQzaDh3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBeE1EY3dOelEwTkRsYUZ3MHpOVEF4TURVd056UTVORGxhTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUURkU3ZIeTdrb0FneC9pTmxKOTBjRzBjWUJEdktnUkhuUWVlWjRFVS9TL3NWNkJDZklTbFFkdlJmd2YKaU9aZWxMRDU0R09nVWFRNUp0OUZvK3J2b1ByVXFnNEFVQlQzN284K2ZDVzNMUU5Pb1c5V0ZDVnJxem43R0pMMApKcUhqM1NWc2FqWVJTM0hRLzAzdk1TbnlBYUhVMDB0ZSsvUDM4blpTbE05OVVEWDNPUEpPcDRPS0hDd1ZnZm9DCkk0dVhGciswUHhMWm1hY3l4eU9iMHNwc0dxOEprZFFGZzBWOVc2bHVaNElWZXFFb2RDUjhSSDhzRFVKSHN6TGoKK3d4UnhZL3k2ejZvc1o2U1Zka2c3aStVbXR6L0FPazIyck4yekRMUGFDbWFlWmRpWTZmcDJNZjRRaGUyamxnSApRZC9iWWFVeHhPbHo3ZHJDWXA4dTN1MDlGdXJUQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJUSlg4QktPdzhlSVJhRWVYOGRVUWM4dTJuZU16QVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQ1hrMkl1UHlnUwovZ3lVL1RWb2pXd3dCeW9Fczc1U2xmMkdHS2E2VkNrZWR0Y2I3aFFLbWZzWDRaTk5zc1BsMXNLOUMyWVdMMDV4CnBLOU94SVJCSFJBU1FaRHdxaitGK2EwUzhnMHk5cVVOS3dOT1lwa01QbEt6bkcxMHRUcHdnMmpTclVzM0ZQa3AKRjBkRTJ6b0l5Szl0S3dYWGZoQmdqeEdvTStWdGJqaC9WamI5NEt4VFBWQm1ka2NoSHZBeTZiOHZ1clcrcUdsZQpRV3RQOXJDNEVxc3VoaWdVYmZCdHdSUmRoVE9BaDFkQ0pEd2lkNjJQR2xoMy9LNW56RXR3c3Zzc1E1WUQ1YmYvCmliZzFuVXVIcDgyU2RjLzB4U2VYaHZqdU1zUmRjWmhKdG5CN2lOdTFjeDU4VWtOa1VSSHlkQWpFSzJpOXRrQmwKbHFnbUxPaTBQRmRWCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          server: https://ksc-k8s1.foneshare.cn:6443
    contexts:
      - name: fs-k8s-app-manager@ksc-k8s1
        context:
          cluster: ksc-k8s1
          namespace: default
          user: fs-k8s-app-manager@ksc-k8s1@user
    users:
      - name: fs-k8s-app-manager@ksc-k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@ksc-k8s1
  hws-k8s1: |-
    apiVersion: v1
    clusters:
      - cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUN5RENDQWJDZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJeE1ETXhPVEV4TWpRek5sb1hEVE14TURNeE56RXhNalF6Tmxvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTWZiCkRzbVBzS0FxT0I5V042c09obkhGTHd0VnJnZlRDRUd3M0hCZ3lTUUp3SE5TekdReWI4K1hRdkRsSForKzA2Nk8KazdtcE5abFo5MHBvWEh0MVNZeEpDK3N3a2hGTW9yQXEybUNqTEZJd1Z5L0NUSW45M1J1a091alJURDVwNWhYSAo2NWxqM3p2V1JSY3pzMXJ1RUhRa2hWUHNJckRRNjhLUnJMK3NLY0RBRWJlNGhCdVhZUU8xZnpDVEpoQWRsVXdhCktnKzFiV0d1TEpZcko5aEpHclI0dmJGci9VZGl4Uk1iUUxrdVhZQUlMQ1RQQmVhZWUxNWpiYVQ2QThSWFZSdnUKZGlIYXdQejdoVmdyaGdraVQ5RklGZGgxSkc5bmx4VWR2NGdLRnkxMjVORnJUQ3FYY3lhRGtLUUorVnVKNXRXVAozZEJzOFAzcWtFQUVtRWRuM01VQ0F3RUFBYU1qTUNFd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQkFKUk1VUm00cFg2cFJhNTQ2djQvYkZKWElNc2MKWXpnK1BhVlkxajFPVjB0T2o2SHZWWVEwUnNuMHQ1K1BaMVhJU1F4MzRPMlNNMmJzMExmQm5uQUFKd25TVVpBMwoxR0lTOVlsMnhJWGVpUTRLVDZ3YjNLSDVmOXJYNGhHSmY2YU1jbTNMRWQzRnBBTGJUa1UzODZVLzJxdCtIeG1XCmRta3Q2TzNOK0czSTlWNlBlajM1VS9IZkh4bU9mQzNNakxDdVZQMU5SZzg0ZUNVb0dXVEtpQjRxNElod2FBUmoKWGFuNDd2Sk5VY1ROSE96dGpxMzcveEZnL0dHdm8rdjVGeGREN2xwcWI4TDlaTWx4NmdpSnVGeERxeFNYOThNMwo5NUFiWGpvNTFsREh1TWN3OFd3S1loRjVqSFh3aFN0NC9VcThzUUNaY0dDVlEycUVST1Z1RHB5VnlRTT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
          server: https://hws-k8s1.foneshare.cn:6443
        name: kubernetes
    contexts:
      - context:
          cluster: kubernetes
          user: kubernetes-admin
        name: kubernetes-admin@kubernetes
    current-context: kubernetes-admin@kubernetes
    kind: Config
    preferences: {}
    users:
      - name: kubernetes-admin
        user:
          token: **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

  ale-k8s1: |-
    apiVersion: v1
    clusters:
      - cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUN5RENDQWJDZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJeE1EUXlNakExTlRVek5sb1hEVE14TURReU1EQTFOVFV6Tmxvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTXNrCjJ0V3FsaGZYTFBMSGVVWitFQVBkQVhMTXZDL2J2bXY3T2xKNFAwNUVzR3ZXZldaRzFSNVhNaE02Sk5tM2Z1bEsKT3ZFU2pkUy94QUNXV3dkMmkySXdNSC9QNjdOdzlqY1NSNHQzL1hxVGlTcUE2anEwNTlFT3BiUzZTaUwzWDMzRAozK1Vzbmo4U1d1c0ExaUVnQ3dUVUM2SHRZSDlNelQrSHh2dm1PcDlKcmJJUThHQ0FTd1RWdnNhL2dpL0JJUWlMCjQwdmRReE50bEc3RGdua1VUbm5UWXYvTFFVVjNudnhab1VqMHFHMC8xd01kMzlndE1icHlEMmpXeGVMc1IrT2sKMERuSXZuSWFTRjFtQkM2dWNEYlBsN09RVkYyS09MVUNZN0NRUGU4REZocnhXTzllMzFoRHhhSkFMUTVkaWVoVwoxaThqMjNOcDRLK0oyUVJPMGZNQ0F3RUFBYU1qTUNFd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQkFGd0VVaVhDVUtGM3JnWXRGelBxNE91dXNobTUKbThnYkZ2Zjc2MUw2NkNWeFQ5Vnl6WHpqMExYTFp3dTZVMFdKQ1U2NXpxMTdTQzlSMlZKUWgxK0pWbFRjZFFodAptVEIrTXFxRTd6SUNXWjRpa2ZaaWJlLzYvNnlSUllFSGwyRnJmRFBFekwxekx5dWlEbTdySzNRQW1MNWQ3alhtCkI4WTBERnZJSmxwbkIwUjFJQ3BXOTZlbzFrdHZwNGM1bzhyd1lVRFg4RG84MU5GUTBseHRjbFg5M0VURFg4VzIKUlZJZXpzTlk2eHVzdTdlTEoyYjh5VDhaVTdZVUFFa0l1bDBlWERrMkxkTTYzdHFNUUErazJRS1dwUU81MDNmeQpnR3h0Zk5SQlkwNnNOSS9FMTVVbHZuYUN2Q3NYSmZQVlRwbEhacnZNWDZycTFOajd1ZmgwS05lRWVaVT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
          server: https://ale-k8s1.foneshare.cn:6443
        name: kubernetes
    contexts:
      - context:
          cluster: kubernetes
          user: kubernetes-admin
        name: kubernetes-admin@kubernetes
    current-context: kubernetes-admin@kubernetes
    kind: Config
    preferences: {}
    users:
      - name: kubernetes-admin
        user:
          token: **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  cloudmodel-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: cloudmodel-k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJVUpGUVprWE5KK3d3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBMU1UTXdOREl4TkRaYUZ3MHpOVEExTVRFd05ESTJORFphTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUMrL0hvOXhWdGhzMFZ3R2haVXNHdlMzbHdhNVpuWlBWUDd2c3dILzVUSkhsalhEUmtrdTFIWWRXSk4KMXA1L2VrT1UrQmVkSmV0cTVBRFNYcU8zSnVTeDZPWE9YM1FVcHhTQ1pUN0kzY1pZWnhRc3MyK05qTllsM2xCcQpzTlpxcFpUVXlJbHNzMmxMZGFhQndScHFRR2FRemhuU3Z3YzIyNXc3QWdEV3g1WEhTNmoyOFBBRWdldDZmN3p6CldyZElZZng2bHUrV0dsTW1pUlFpdGRRRFYvenBsSGMvNjRVUnB6Z1k2TnQ1Ri9wMmZGYTlVeUo3M2p6VldBc04KVFBpVERrY0l0dW9uNWV3clY2d0F5bXBoNFlTS05VOWN0YTR0UzVuMFpDTEpZbFFObGZhTGN0S1ZXNXh5S3lpWgo5TjNSM1NBNzJOVHJHTXdBT1Z1Z3krVXlMU3doQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJTVVhIeFhIUXY1YVl0dEx1SVZKS1NyenVnZUNUQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQnZPZHRDc1lXSApBYUhRb0kyQXBtVzlKd2lMWE1sQTVsUGYzczdMVkczRCtsTXVPVFRFVVBGeDFXdDk3R3Q2Rkdxa1A1OTFMYnduCkdZajM5aTlpWGI3bUZaWHpiOWdpUzlOTjZrYkZuM1RTdm5QUEtVMXdsWlVtT3ZLdVZ6Y2c5QWVCeUNjaHZZS2sKMkN0Y3JBbVI1UXdlcWMyU3NtK1c4SmhhK0xjOTlOc2tqZjdtbkVqaU1HYkRGUEE2TUdZYyswUUFMNkFYQU92MgpxWndRSnBnK3B3cGtEczdoZVF1Q0xVckR1VSt4bk5FbllBRnZoZ1JHdGpqbkZteDhia242UVppWE5SQmhGZ0RuCkNBNkRsSzVWcEVGZWpUbTQrcjdid2VDWkJuSzkxQ1NxeEpRQkF3Q05tWTg3bEFDRW9yK2s2cVQzcEM3MjZYTUoKNDRaMWRPdTlrd1h1Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          server: https://cloudmodel-k8s1.foneshare.cn:10006
    contexts:
      - name: fs-k8s-app-manager@cloudmodel-k8s1
        context:
          cluster: cloudmodel-k8s1
          namespace: default
          user: fs-k8s-app-manager@cloudmodel-k8s1@user
    users:
      - name: fs-k8s-app-manager@cloudmodel-k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@cloudmodel-k8s1
  forceecrm-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: forceecrm-k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJTjYwY3JkV2tnQTR3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBMk1UY3dOekl6TlRGYUZ3MHpOVEEyTVRVd056STROVEZhTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUNtOFo3NzEwWmlBVmN2RmZhWE9GWXIwSG04bXBGRFlvVndzMWZnZFpzcjg5cEVNQzZ6cTZFSyt3Q08KNjZvczl0UzdHK0xlMTZyMXZVODRudjl2U1ptZVNpeFBYYnlRVUJpbXd6WmRidEdyck1QZUwwcThSZlQ4RDZ1OQp2d0lIM09uQURSU3RnaTNUZnhkU1BlYm9yTjRTU2xlaHBPYmFCNnFUVS9ic0doYktMbGg4M1lnTWRWdmJtQ3B2ClIvdFNPTnd5TExEUjZwa3R5Wk05SkpBbitUZHBOTUozdVVhR3pWVTdIL1BsMDhBdHFBOVg0UkFpM3FtRnl2UWcKRHRld2srRlFGRzJPNFQrZ1RKbnJ3dlozWjVCYWowQWR6VTZ6NFZiRzJrMmFkd1FLbTF6UStnazdPK2JWcEJrNgpkZjZuRDZoSEJsY2J0NlFjT1RnRU1uWGh0OC8vQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJSWEh0cnJtZXhWQWl3bkN0S2VkZ3ptU2tMdnZ6QVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQTROZ043KzIvWgp3QmgzU2lncUlLaXEyVDUvNzBJT1pXNjJGNm4wZlZ2TU5PTlhRY016NXlFSmVSRXhqYkVBVzBXZEh4T21HcXJ5CndxZnFjVzZmZk81d3RHeVMzbjFVVWF2K1liY2FnVWR0aXlJbkh2NktiUkRXMVB1WXFaZTl2R2tPTU92NFE5MmIKNFBJMWFHN0dCVnBHY2RaSEhidWhVV1VHK2U1eG5XTWhqaFBudk4vUERzVHFtZnBXWTdnZENnUll5UVhWU3hhZwpQS3RORHM0MnB6a2xUNkhrS3hsMTN4OVFib3ovY3FhQytJYkJkL20yWHNiVkFBVGU3REs0eHNBeDh3c1RwYlRyCnh5V3FUREZpTFQwM0JWb29FeDhWU2JkMm1nbExYUzhPUnJzbU1zNlk2anA5L0dsRUxiL0Erc0RicjFOSEw4eHQKV3I4a0s1L1UvWVJjCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          server: https://forceecrm-k8s1.foneshare.cn:10016
    contexts:
      - name: fs-k8s-app-manager@forceecrm-k8s1
        context:
          cluster: forceecrm-k8s1
          namespace: default
          user: fs-k8s-app-manager@forceecrm-k8s1@user
    users:
      - name: fs-k8s-app-manager@forceecrm-k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@forceecrm-k8s1
  xjgc-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: xjgc-k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJeU1UQXhPVEV3TXpJd01sb1hEVE15TVRBeE5qRXdNekl3TWxvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTVJaCkJNY0l0ZWRvUHRzaCtCT29QcHJhcEE5aVNUazFKVkQ3c3IyUWdSZnJxWFFvd0piZEZFaGdncHNBMDRBM1JhUUIKeEJJWXROaDdtS3cvdGlXUm95bVMvK3I0UkU3Um1XN29IWUI0REpSbEFkTzN1ZlRqbC90ZkVXTUZ3aXppNldLRworN2diVmRwdDRqM1VocXhkZWowVTNMMERUM2c3eXNZaWkyZUpmRTg4ZFFUbHM4czN3aXo5Y0tsbDgvL1cveWszClRmWnR6ZjRQL1Jndk1OOFA5U21BMncxRTBXUEp4MUk0WWEzN0lUNnFRQjJMWFI2bDFwTzk4ZE9paURsZzVHWEMKU2RmTHhYYkFISFJDQVhpRnMwUWVDRUs5ZzIwL3hrMVVyMWtpMU95RmtPcTZiZ0MvMFN4U2RzVXUrZUE1SHl3NgpKUVE4Mm5PVFVaVnB3OUkzNUJNQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZQZjZpZDkyemxhLzJnVU1OZlN3MW1sdWIyN1NNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBRUVTRXptZ3MwWSs4WXR0N1dVagpDR3drVWRTbXpNN1lyUktWRDBxWFFZQzRBaEw2cTRiZzliZXhFdnBvTW50b21WUFJkNFlGZXo3SGhvUTdENHdBCm9LWVAyU2Z5WDlrckhQdEFmZkFhbE5XWDdNRVZGRzdOMDBZTkluVVFTU0JOTmpNWVZueEppd0hMOGtscElDQ0cKR3dPQUlUVVN6Vis4aEo3a0hRblhuSTR2LzRKb0ZmZXBoeitVZTdJcnA1VU44U0FycmUvcnRuS3RuTXpsMStVYwpIM0grQ0E0cXUzUm5RYkozNnhsWm1oaGQ0SkNuaTFERDJzYVZ5MkZNdjExSEN5NmlGcE94ZVVQcXdaUE9UTU5tCjFaU2Rmalc3RmhkenB1TGh3c05pVFlibkdYTVViWlpVYmRFTkdNd1pZcHlVVFBKV1ZKR1VrVlk3aTRjaTVMdzQKQis4PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          server: https://xjgc-k8s1.foneshare.cn:10026
    contexts:
      - name: fs-k8s-app-manager@xjgc-k8s1
        context:
          cluster: xjgc-k8s1
          namespace: default
          user: fs-k8s-app-manager@xjgc-k8s1@user
    users:
      - name: fs-k8s-app-manager@xjgc-k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@xjgc-k8s1
  hisense-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: hisense-k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJeU1UQXlNekE1TWpZMU1sb1hEVE15TVRBeU1EQTVNalkxTWxvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTSt0CmVNaHJpNXZDRFlOU1hsbzdZVWlxSjJkdldxQ0tRUFhDOTFGbUJ1Sm44cXllNldLV3VBQzR5K3lySmxSV0FpNFkKTmg5WFhvUldsdFR2cFZBMUxVaHBPSzVpNG5WanFNcGZ4WnM4U1c0c2dqcm1CU0NpdFIxekdielJKc1owTXBvNQpkVUhTSWZCbmJuY1p3MkYwUkQ5ZGdTeWpLV3A3aFZqUHhsc2N2UG5vd3drU0JQMHlLUTd3eHoxZjFrblBrdEE4CktqT3NLVTRkRGVnR3hvbDlyZTRIdnFRRlR0UU9MelpmTEg4NWl0L1NLZWlYcC9CWVpYSnlTNnpqaXRpZnMxZi8KZU9ZcEJaaWY1bkt0bmliaVZJOFAwMjFPN2E1QTAwUGlxS056ZXdGeU50cFF3SzRaVjZRR3lvZFdFa0VhbURKRQpZSzBYWThReXAvTnQyN3dHbks4Q0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZOUGRFTitQb2l2Q2gxNGwyMmR6N1FuOEc3UUxNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBQ0NnWWVlbVpJWjJxTzdyRnRsdwpERWowUkNhRDBjcFpEMlAwK3d6dnRTQ3BxQVltc2xrYytnOFowQVN2R2JUQ0NmZHY1bHZycUNOMy9NV09XZzAyCjZSU3J3eFArTzVsRmNDZDl3RmhVYWtNVEd4TUFkNmVObGVCVk1WbTZJQ2NRQU82a0NPNVA3Zm9tMXY5cVJpbVUKTkFzd0ZXR2xBZm9qZXphZ1V4YS9zeEwzZ25DeXZpZGMzY05KdmtyTmxYUXREWEFNUFdOS0xYSVM5aTBMS2t3OQpEVmMyMVUwWlFkMWFyYW5KTXFQMHcyazhWSUxzZ002dnpDRXJoL3BvTnQ3ZS90THFQbjdHUkUwNHo5R1R6RzNSCkFhYklmRzJsZjI5WXRuUUlhVkFLSUJCUU9LZVY5RkY5QVVVL3VEaEl2dDVxYXpFMEtYOFVoOUlNLzBqdlhXK0IKZmM4PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          server: https://hisense-k8s1.foneshare.cn:10036
    contexts:
      - name: fs-k8s-app-manager@hisense-k8s1
        context:
          cluster: hisense-k8s1
          namespace: default
          user: fs-k8s-app-manager@hisense-k8s1@user
    users:
      - name: fs-k8s-app-manager@hisense-k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@hisense-k8s1
  chinatower-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: chinatower-k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJek1ESXlOekV3TlRFeE0xb1hEVE16TURJeU5ERXdOVEV4TTFvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTVY0ClhhM2lhMXRubzFCVmxLSE44TnFOU3pZcFRJV1BsUWE5bWQxeDNWajljVzgvYlovc2dUdVZ1SUR2aFVTb1NvNUUKbFE2ZlpZblZlYkNUMlJRMjE3YUdaV1EwcnFkbzdpeUloUG9ROXJERlVmQlVNUUU0bndsazkwL2pwWlNHV2FvNQpiRXRtQTg4RkMwUS94emJJMzhNNGdIamRpRWRyblhIbkIrbVZQeEM2Q05LcGYxbHhQNGNVWDNlZGMvV2pHZ29yCkhjRXhyTEtrbjQycGNFcjROemJGcGFXcDdFd2p6YURTanl6TW1PU2NGVjJzSlE0QW5ocmhjYW40dGZvTTV0WUYKZHhPVmg0clJxLy85Wjl0MXcrM2g1c2NvQjh0b1V2enRERXlreEppZmVIQ2paU29xb1NUMU1taDNseW9XRHZ4OQprV01NZllUQ0hLSzRyQ2tvRlVNQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZIVWFZSW5BRmwxTm85VTBaM1AxRjI2UWRCOTVNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBRUZuV1p1cWV1NFNlS1diT25uYQpYdlVyMTNZZko0bWFxSUs3ZUZPMVZpcFR5ZWVuZDh2WTBXRHFQanFCbG02M3hrOXI4emo4NlhCd0NseGZ6VUxaCmVMV0dnRVAxb21EbTVYc0ZrYnQ0Z3M3ZnFycE9kc2dxcUJ5eXFhZTFiMVBuRXZTR0dxWStiU0llRzN0bkZlZDUKRDdxMUllRnJvdmhITTIxcktFUkhWY0ZacmZhZ0NNN2laaWx0VlNISUZpTkFHWkZIQU04RThGSllTSytydjdsSApadFhpUTZUNXNMMjk1NFNRT2J3SEd6S1BLQzI3dVN3c2tkU3NvNyszblZEeDBzWEtLaXBzaXQ0UEhFMHFQcFZxCm51U2NxWlpzcTVnSEpONW13SWZQU1RUV1pDVVJSOFZSV3ZncjFHeHI5RkdPZkhySG1zdFgzcmJjK3JtMEQvcGwKUGQ4PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          server: https://chinatower-k8s1.foneshare.cn:10046
    contexts:
      - name: fs-k8s-app-manager@chinatower-k8s1
        context:
          cluster: chinatower-k8s1
          namespace: default
          user: fs-k8s-app-manager@chinatower-k8s1@user
    users:
      - name: fs-k8s-app-manager@chinatower-k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@chinatower-k8s1
  mengniu-k8s1: |-
    apiVersion: v1
    clusters:
      - cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJek1EZ3dOREUyTURRME4xb1hEVE16TURnd01URTJNRFEwTjFvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTU1ICk12Y2owbm9qT2szVTdLdER0b202RVJrN3ZpZjdGbEJPTGI1cmppYjE2b0w2WlMxK2liRkNIcXVjL2JyMVhGb1EKMVVhYjhRYjZDVzNRYWJJT2lSSjdBWGRTZ1V5Y25yeVhZb0h1SE9SSVpjWWhqM3l2RE1tVkplMXlHZ1VFbGxxaAozWWNYdlZnTnIybkpDbjN4REY0MmlRTjZSRWVNL2hSNnpydkQxOVhUb0pPcUQzYlE2WU1zYmxFZmRVczNIWDJpCjVFMVE5MkpiMmg0ZDBNRnVyckpLKzJJeW1GN25PVVljaW1VdVlyRTBoditTeWVDaXhpRlRBRlZEYVZpWmlVYlMKNzFtUEhsRnpvNEZteFVyOVp5SjhwMlRidEc5YUJ0ZkJMWVBzNUlxN05SdzFJNG90TUZHR3JXR05xelM2MUh6MwppQnMzbjNnd2hJeU45c0ppUmVVQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZIZFBUOGNWS2h6djBsRVFDYTlmN3M0NXQ2bkNNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBRXZHWmE2QllZQmNTY3dKT2liNwpPR1Q3ZExBWlhVcnFNbnJPVTQ1d1EwK2V1ejYvWkFYRGpPUlludU40dzlUYVlXeHJiTzAzWFM1SkdUb3dYazZ0CmpUdlZkYk9lS2YzZERlSWV1YnJTU1BSNUttNytMMlVqekpVWCtDMWp5UklWL2F6VENiam5hTkJDd3JFTGRlby8KV1ZnRk5TRmxlME1lUlpQNzhLeVJrN3NTM1pjUlNhV0hhYU9RdUtvZkxndlcwS2E4MktJckR5WlVSZElFVkFxZQpKdHAwNVZHWXlkdlBmd0FaR1F5YUMyeERTamg2VEhnYnV3RURRNDVEZVlrM2FXcUlydCs4ditsSkl5V2cxWU55CndiUDk1NTcrRmxkNHlSWlF5M3FHUSthZnYxaGJ2VEdTeVVLa2dtVk1nSVlTSzZiZ2d2ZzFHQ0xOeU96UTlyQ2MKam13PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          server: https://mengniu-k8s1.foneshare.cn:10125
        name: cluster.local
    contexts:
      - context:
          cluster: cluster.local
          user: kubernetes-admin
        name: <EMAIL>
    current-context: <EMAIL>
    kind: Config
    preferences: {}
    users:
      - name: kubernetes-admin
        user:
          token: ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  hsyk-k8s1: |-
    apiVersion: v1
    clusters:
      - cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJek1EZ3lNekF6TVRZeU1Wb1hEVE16TURneU1EQXpNVFl5TVZvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBSnlVCnpIbFM3SHNUeHA1azd1ekZrdndzMGNMVTl6dDhxK3dpbXB1NlNtWFdtNlNmWWYySjZ0dkxJTmRIVzNocWQyZEoKSkZtd1JlVVdmUmNHQ2JmSTNaRWxMZHpSclIycUJFYUpDcFVMRERndm1YNk1YTEdTTmJReE9EaWpyT1RBSXo5dQowQ2ROWlJhVEhkdnVmMDRCUys0eHJpd29TRHA5OUhCQTJrODJMU25ySmYrWktCYmhZWFdGQmxZdDBMUUpqcG0xCnhLZ3I5MC90TXFoR3VOVjY5Qlo5Um52TnptS2pTU3h1cTMwMmhuWEVRUXEzOGM1ZUZ6Q0lEcCtnck80cU8rMDYKTHFEakJZQzZCTnRyc05xRDc2RHZtanc5LzBlSmV1VzZjdGorcUI2c2ZRb3dLeWFyTDcxTWtpVTIzRWpVV3k2dwpEWVRGbzlMS2hqTVlielgrd0xrQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZCeXJteWZza3k4NFdhbU1XbytxS243djRXMHBNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBRFB0THovWWhHcU44czNqazJJNQo4amluZHRRd2Nmclp2WHl1eDhodTl4dHdrSXh5SUx2VWlyQmIxdXkzR3oxbHViUWdrZ2g0NGlabGsyVVo4R0dICjB3Q01JZU1pZmhlem1JWmJGY2NmQ014SHBINUVTYTE3emR2cC93NTh2L05WZU9kNnJKVmFPaFhWVGN5eVRpdUQKU04yRXdhUXp0cmw0Y1BHZU9uS2Q0KzRhYmgwWmJlb0NqWm0wcHJ2eWlDK3JhMHhoUjFLWVBNaXd2RVF5dFExcQpBeUpDcVQ4aXh0NjIvMDVIVXhWOUY1NWNrc1lKRys4Q1dtMlZ5Yi9vdnJGMWhmbDlhNnBYeDJ6RGF4SW1EdWZXCnUzaGxnUG1Uc28rampkZXVPUTBYYmNiVWVTZnRJYStwSFkybFpaaEVpemdhWVI0ZjhhUHZJbDJyRjk1cDk0R0YKZW9JPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          server: https://hsyk-k8s1.foneshare.cn:10143
        name: cluster.local
    contexts:
      - context:
          cluster: cluster.local
          user: kubernetes-admin
        name: <EMAIL>
    current-context: <EMAIL>
    kind: Config
    preferences: {}
    users:
      - name: kubernetes-admin
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  wuzizui99-k8s1: |-
    apiVersion: v1
    clusters:
      - cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJek1URXdOakV6TXpreU1sb1hEVE16TVRFd016RXpNemt5TWxvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTENmCmVXUTdUK1hRejFZS2xLd3EydHR0Z09ZajJRWUV0bHRTZVBUWWJMQ3ZMSUxwejVPekI0VmpUaTVwNGhWeXZoTDAKbytoZzJpWGNPRzNQSG9ZOVBzbG9BUjUvOFZaMlo1NVd4UGQxZ0g0Mk1wSTFWQVNqd0w3N0RudWFRMW5aU3dacgphMVhualg4YUZCN0RyS2xvcUtsZURRVElaa1dhTkoxbmx3QWVCazVUem9Obi9UdGRrRFc0bExrUitTSSs0S29pCkU5L25DSG96UXRibS9IU0VVTXZRQ2V1SUxSVWxXcXI1L0djS21wWWdVQjEyOXJFcjVGdUx5NzZkVGRXVzBFaE4KWHkzeEZDVU93bkU0cXl5SWRvTVE5eHBiVjFoMXQ4bWNpM0RuV3RCakY2OVZIeEZhcDhjNDlzTld0aDZMbGZrcApzOSthbWxmTk1VNlJKbWZsU1IwQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZHTkwzZkdVQmxuMmpyVzliUm0ySkYxWTF0YURNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSWdBWlNmc01QelhUc0xnZ005Twp4R0hGdjlZQUFtWFhnQ09LYmtYTHB6TWI4TUV4QzMyVmdUSUJnT1dscm1vbllXN2JQaWRoVlZpVHcxeWpNdmVGCm4zUmcrU29HS3lYUVZNRVI4YkczdFpSOFk4T2lMM0l1clJKbitrbWgwLzRHSERscVJubzNPdG81Z3FYNlU1RysKc2JpaEkrdlFrNzVWWkQ3NVYwUktUTXdkbTY0R3RqaTZnQ090ZXBWaXVmSkVsVHhXMW9jeklWdWx4T2ljRFN2dApvVHJiSitTWW1udlpMVHdmcGRaaUxocjNFeWlSSHVSSmRGRjdsYnR0blEzVXFhZUQ5Qnd1VlBRaHVVQ1VRTytvCk5TekZyQmFUMUZCMkh2aTROamRla240RE1sY0V2Q1Bqay9xZmpoSitvajJRcWJZY2NKQ0VMajdyTXdiWTJwRWsKVGNvPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          server: https://wuzizui99-k8s1.foneshare.cn:10183
        name: cluster.local
    contexts:
      - context:
          cluster: cluster.local
          user: kubernetes-admin
        name: <EMAIL>
    current-context: <EMAIL>
    kind: Config
    preferences: {}
    users:
      - name: kubernetes-admin
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

  iflytek-k8s1: |-
    apiVersion: v1
    clusters:
    - cluster:
        certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJek1URXdOekEzTURReU1sb1hEVE16TVRFd05EQTNNRFF5TWxvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTFVBCndQUFc5dkFxQjhIaHYvbkx1aldINkJ6VG4wRldOaGc3Q3J5OWZPcExiRUk0aFI5dVdDNURsaHJpZWJZSXpmOWMKNTNvcm42ckxRWmF1ZTdjVnBENmh4bk5yWjFCVkh1OUkwZjhzYkdlVkZtNXkxS1hpc3hoTktHcUlLRXlnb25kWgpvTzV2UXlSYTFCVmIzemVtYkhiVElMRlIxNkx4M2I1RzlSSVFaNmZkd0lpRC9SMGtNZXdPcVlnaVduVndVSjhDCmNwdjkwUlhFRTBHNzlGUzVRYTFUN3poWjJoY1M5K251U0Nwd2NIcTdqNGlwSVVZMXFKVWJ6TFg0THhUOVpRbFgKTHJnRmsxcjlRY3p6bE5PMDdneGV5cUJqTk1XYjJ0azdrY3lsa0tCUVBRT09OQ0djTGdFSzFaVzhDaGNxVURIUwp5OVBDY3NSQ2tDNlVxU2JDSTdFQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZGZkxBdFl2WXdGQ3Z5MzVLUmVVL0Q3eUlOSnRNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBR1NuMCsydGVMYVNGL0tWNHNpSApVdk1rSXVTc2ljYWVCVDZ1U1E1NElWZ2h3RWdidmhGbGg4NnNCZGROZkdNSVlJUkl4Vm5oN0d6K0FWQmRGbDZKCndMYi9QejZ6cjQ3SThZakhNV3RsTHpZQkxuQUVTcFh5MUdVWm8vcDhxd0VOMElUUTBxM0VmR1Ira3FmcUZQRlUKN3BJUGtDZUdFOWY1ZzFJU2VEc2lQMC9jcm1oeGprNlNQbWE5eCs5SFhieVp1em81WFN6VVlJcnlEZE8yeStlcwpBUkZmZmQzSTdTMzArdWpBSXRTQ2tuSFM3OUx1TGlzcVJBdHo5VGtRT1pWV2YrZ1NUc01US1UxVXh3aVVHeTRuClJGdTlXMW9BM29HWVJpSHZMVU1jNVBnd3Azb1B3eFhGdzZQakZKeUlOeUFLMXFGTFBzdnJnSkNrVll1UklUQ2oKTTNzPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
        server: https://iflytek-k8s1.foneshare.cn:10168
      name: cluster.local
    contexts:
    - context:
        cluster: cluster.local
        user: kubernetes-admin
      name: <EMAIL>
    current-context: <EMAIL>
    kind: Config
    preferences: {}
    users:
    - name: kubernetes-admin
      user:
        token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  yangnongchem-k8s1: |-
    apiVersion: v1
    clusters:
    - cluster:
        certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJek1URXdOekF5TkRjeE9Gb1hEVE16TVRFd05EQXlORGN4T0Zvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTmVNCkZENGRQeEptdkhWRk1TVzhocEtvaWtPVmdyemZKU0E3OG0zcGRNQ2d4OE1TL0hrZE9vNFpTUFNuKzRpdW8rbjkKZU93RWtJNk9Nb0srWUhoYyt2ODdIbklrTUlGRDhmWitvbXVVd2graWQ4T2lubUxNSTd3T2lhbFdleUt5eVVPRgpjaktBMTdra0tyL05oSVlpa1BSY2lkVTB4YVhoWjNRT3ZmMUh3bHJZNFVrMDRDY3dNLzBPOHhKYzFmcXgveEJxClVHam1pdzdoZHlWeVJJSUpUZ0tKMEt2M21IVWUwS3ZoSHJzRFJRbThxWCtHc0V2UEdRSlViOWUrV2ZlV2ZiWlEKaG9yVVZEQWVkS2djS0ttTzBQTTdLTm4zaTJ3Wm5zTFQwSHduUjBQMWtZQWxJZEpwSWtMQnZvMFVHbTlYaFBnbwp0eUNPVnBOQ2YyOWM5eGlxL1JVQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZFaGtLRy9xQ3BFWWpmcnZrNGhydUhJc1JLcStNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBTDZaVW8zTmljcnoxbDdVQy9aOApLUlM0dW8yRUlPT1ZmWTlJaXR4NFNEd3pwcXJsamp4Yjg2ME5iRytvNnFuWW5zaExTQ1RVMjdSUWg5Zk1CQU5sCmRCUzZpT0N3S0piTks2NVg2N0IweWQvSDFaL2RyTU94eDUzemlZanVXTWUrM2VhcUkxTExqYkJsWmJOaU80RisKUms2SmhSV3pkeDJRb2xOVjdKN05QQVVaVVRMRkdLWE1BOHF4ck52SFgvdU0zdUNIakZOVVpUV1E5UzNRSDZtaQo5bnlQV0hzUjNUWHZRMjRrdmV1T00yY1RrRkV6eHpGSUdxWFpVa1RFYkJqMVhDSlVPdXhBL0lJRFNaMXNTcnFECmQrL0dYTmVaOURXdUQ4T0RmVkU2Tm9ZWXl1RlBBZW5ZZldlS2F0bWk3bHJmQzIvcXZnV2h4UGpxTStSYW5TbW4KeWtVPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
        server: https://yangnongchem-k8s1.foneshare.cn:10187
      name: cluster.local
    contexts:
    - context:
        cluster: cluster.local
        user: kubernetes-admin
      name: <EMAIL>
    current-context: <EMAIL>
    kind: Config
    preferences: {}
    users:
    - name: kubernetes-admin
      user:
        token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  hexagonmi-k8s1: |-
    apiVersion: v1
    clusters:
    - cluster:
        certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJek1URXdOakV6TURRME0xb1hEVE16TVRFd016RXpNRFEwTTFvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTEp3CkFCazVpR2NkUk9ET0dxbTFKSHlTTWhKeFRmQVdrR0RXYzZpcS9MeERUWHlRcUQ3UFBpSEMzV2doQUsrSWZDTEcKMEpPWDlBc0JVbCt3T0l6UG9NUkVrMXdyYWRHaWd4d3pEa0Vjbk1XelJWdHNkSHd2L1FtTHVmeHBZZWVQMDFPWQovVGw0WjU0Ti80Q1RzcVdjNUVFMk04Kzl0cXk5b3lVb0tCbEpvdEY0ZmFhZUFOWWprYjZFay9mOVZJcWw3dE5jClFZQzV5WWtxNks5K3JUWkVJNzVnVTRjTVNGLzhPNmNuZDVlSzh2R2RaaXM2Smd0ZWFwOFlnU3h1b3lmb2NPc3gKNlZjLzlzQVNxbUZFVm04M0d3QXRJNWZKZGduWENRVFVldnFULzcxdThqZGNmSjBMTmpVcDFQYTl3VENOS1p5dQo5UkMzS3d5OTlmNEowVXVhRW44Q0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZOQ096L21pdlc3NGRGQUI1QmZnYVBIZzNnTFlNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBS3ZGNlZFTThqWXBTdEN1SHRYcQpDbkdmNll6TEF1djhqc3pNZ25iMjExcXNUR25JUjg3Y1AzOVF4dERFWGJaa00zVnhiOVY3Tm9QNVNHYUpkQmtCCjhUOUJrYWtUK2VjbGtBM0x3YjduUGp1S1V2NWMvQlNsWkRmUzFGRWltOTFRelg1ZnluL0JDVnpzdGVHYlN4N00KVm95T1BOSkNyUTRHOTBRUUJXbHEzSXdCcllIdGdXNzVyc2hRL2Z1aDZrWGFKQmtJRDJyZFBSL25xeHdVUXZ6UApZRXdySGRtcWhEU0I3NmRta0ZWTFZzODloWnBIZ3FvZ0JqOEpjWUp6NnBwWmFZZjBQYWtIN2tEbFAyZnFHdGFzCkRZdld2QmZuclJoVjlYN1ZuaFJSeDBHWVlsODltZUJiUDlGaWhLdXo4REZONkRwNFcwNEpNTnhaN1JzbjI5ME0KQnBVPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
        server: https://hexagonmi-k8s1.foneshare.cn:10185
      name: cluster.local
    contexts:
    - context:
        cluster: cluster.local
        user: kubernetes-admin
      name: <EMAIL>
    current-context: <EMAIL>
    kind: Config
    preferences: {}
    users:
    - name: kubernetes-admin
      user:
        token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  kemaicrm-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: kemaicrm-k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJVTdnYUJKTDhQMVl3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBME1qa3dOalE0TURKYUZ3MHpOVEEwTWpjd05qVXpNREphTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUMxSDZsVXAvSSs0T0tjQm0yeHNlbWZKZEZHZWR3bkQvZFlEc1F3eHBzZld1TE9XdkFpU2tjOGM3bFcKVkYzcGM1YW4wREUrODRSZkNRTS9GV05PQTBMdURsbHkwSnpSQVJKeXlRenIzOU1yREQwNEpiMmhYZzg4T3pUZAo5UFVuUzZZN1laOHlLRnhDWjNBOGtaMGxuTS9oTUhySTU1T0pyN2dLdmhUR2x5U1dkMWhvMEZyZllET1BPRnQ5Cm1qYnE5dkxRM3NFckROUWU4RktzK3pkU1o3bUFVdFJjWlZWOFdiM2Q3WUVZNVdDbyt2dlAzeWVGQklKbnJ2eWoKNWZONFV5N0NuOHlGUHZzeDJwSktQQklNUXN5c2NhZkNmc2s5bkJUcnIzMXJsVmFLQ2JzOWdnRjRCSGUzSWZlTgphKzQ4MktHMHFvS0UyRldoQjRmdmVZRjg2cVRwQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJSb096bFkybDlIdnBFWjdJdmY0SUxHajVMN0JEQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQXRVSTlCcWoyUgpaL053c29teUN1R2tGa0lQTWJpTEw4TnhrVDcrZ0o2M3JCNTVFbXlZczJVQm5jLzVaeDhsWTYxaWt3ZXc3a2pWClpqMDVCSHYrcnJkMHVydnY3QWh2ODRnU2VCNUFIS1RlVWg5VHdZRmp3KzlubTA5N0hsRGJTem9PajJjZTV1TlQKWXFHZXUzSUtQeEFQb1B5alo3Y0NjaHhmanF1RHhUN3piZUJFZ2FqU2NRc1dqbWhseGxMVlljL3lyZ1h3OXl6SwpaUGFJeGdPbVJTZzFSYXRPT0RPeGU2KzY2Z2JRenlOUmo0d280endYVDZJUEpZN25ZWGh5Vmg1TERiUjZjbFI2ClpLbTZNZEpINFlmRTNlKytJTGdoaG5iTFV5UTJuNmJ2Vlozd0JPUTFleGhISlpVRWQ5MHRpWUxva1dQenU0T3gKK0VDT3pGaWI0alpOCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          server: https://kemaicrm-k8s1.foneshare.cn:6443
    contexts:
      - name: fs-k8s-app-manager@kemaicrm-k8s1
        context:
          cluster: kemaicrm-k8s1
          namespace: default
          user: fs-k8s-app-manager@kemaicrm-k8s1@user
    users:
      - name: fs-k8s-app-manager@kemaicrm-k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@kemaicrm-k8s1
  allink8s-k8s1: |-
    apiVersion: v1
    clusters:
    - cluster:
        certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJME1ETXlOekEwTXpRd00xb1hEVE0wTURNeU5UQTBNelF3TTFvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBT1pJCjdodnAxZVFFa0lGRVFBeVNGeE1OSkxpMnFoQ2VHWnhxZDhTNnZ4K1E1aytaNjdtMWgvTExmdGlKRHZaZlNNV0oKMzBHVmF3b1hmanZucEFuandrejB3ek9iSmprZnV6d1FvYmNlY2FibGk0VXc0MjU1bG1semp2VzBlZmxkWmt0MgozV0J2R1MrcHFXWHZMTnBZNDcwOS8zblpaM3hCbkNocHp0TlozSzluT1ZIdTNuZFN5NUZEQS9ZSlpiSXJNSzU4CmNYbFBCYUdESk9uYm5WaDJoUXZ2cXJxNCtiYWN3VS9KcUR4OEtDV0ExeElydFNYOVJ3UENldHF5c1YvcVhpY3gKTG8waXZRWEluRnhZYVhmY2F2bUxIeHlLejJoQ3lYbkF3SDROS1h2ekdOYWUycitRMEIwa0VpZWdCQTFOZmZCSQpEOXdNTDVTY3RZUFM5UTZrV1dNQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZPcHlJZ1I3WkJlWFFlTVBPUldVb3FXbHhPL1dNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBTXpzTzJDZXhPN3JRay8wek9sTAp4dk1aTEtJdXE4NTJlV3Rrb0hOVlh3NFZqRTdxMCtSK1ZYOEVrRzRwN25HaVllZjNtZ1RqSmZGZ0FEV2szTnpICjJmNEtPL3F6QUo0KzVFbW5odGdoNmhRdGNJQW9JWUpqemlocDVwQ1dQalRRUnB6VUU1cVA1dDhwTldObWZoYzYKZ0xxY1BEVzJaRU4ySlFoTU5aTEI4dTBGRXJWcEpxdVdiZSs3alBmMDQrMDVsRVF1UnJhSEtXR1lVN0d5WWdXNgp5UlR1M1pMalpaamF4QzRhbHlVR0VVQkRUVTVRdVZyU3NqZlZsdEx1eDNYZVczdHFRKzZGc1k1OTZtN2xFbXorCnMxU2thUTYrb24yaFNKOW5uWjB3cDhySDVnaFBCM0ZlcWJ5TXUwdERaT3NoYTNsM0lwWHdpYnl5SkpNcHA0SkgKOWtJPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
        server: https://allink8s-k8s1.foneshare.cn:10254
      name: cluster.local
    contexts:
    - context:
        cluster: cluster.local
        user: kubernetes-admin
      name: <EMAIL>
    current-context: <EMAIL>
    kind: Config
    preferences: {}
    users:
    - name: kubernetes-admin
      user:
        token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  forsharecrm-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: forsharecrm-k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJZklEdVoxWlh0ZlV3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBeE1UVXdOakUzTlRKYUZ3MHpOVEF4TVRNd05qSXlOVEphTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUURYQ09WZDk1Qy9lbVpPMFB5VFVTRE5TSHBoMGRIb25MZC9MSmRkUGJVdmh2KzlIMm5iU21OanVRVFMKZ1R4OTJTU0JwT0J5RUozUVkrSE5LYVBnaEFvY3pGNXBjWkhpeXF1QWFqZ0UrN2xqdUZQa3lMcDVMQldWaW9IaAp3S3JONEhHWHJCM3R4RDUzaTBUaWU4WDJFWjJyellUMXRxSVVTK2FKUlBuQStyZUErMWFTUE9Na0p3QTgwbkNVCnVUUERlbDlCVlRMNVhhMWNtU3JTcGhJLzN4Zy94bEk4K2ZMc00vMHNlSTZJR3hWR2I4N2NCOVhzR2gzcnR6UlgKcGJIZVprZkxEd09neHNPMTJSc2hvS0FOODNZRzhzZnRITk5kZHFKQzFLTU9WSlBrQ0ZoZ2ErVEEvcUp0SDBIdgpCWktaeUhFdnRUNUQ5Zml4eDlQRjRvZjRhdXpGQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJUM0c2Vzd4WWt4SUxpNlQxWU15eVdad3o2VERqQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQjBQV05FMXNYTgpjRGYxS0VmbGZUQmZZODVQbTVxbHNSODNObnM1cWhqdFNTcGJUWUUxVDZ5bVBLdWJPRkVETHVtazcvZnJDU0g1CjRyRHBZc3h5bXVXWVkwSTQySE5uZ2Zud0k0WkwyMXhId1ZaUlJtMitobUlDa0VjUlUyRjBld09yd3dQWUdrMnQKTXpqUFFTTHlMM3IrM0JQenJVeUE1STgwOWdQeWgrL055cmlNeGhxemVBZmU2c1JEay83eDQ1a214aGpYT0xTbwpCVzJPZWttZ2ljajY1RDQ1SEdnb0NLbm9KekFDeG5YbEZ3TElRMXhJOFVTc3lFSkFWY3FSRGNsMzVFMXdzQXJ2Cm9INDNLNWZjWmF4blpQbzRWMlRqOE43Rys3K0FDcTFPOG1PNnlnTm14MndrS242TUo2ZnRoa0pBNDIybFBsUzUKRVgyaXM0N1Y4YlJjCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          server: https://forsharecrm-k8s1.foneshare.cn:6443
    contexts:
      - name: fs-k8s-app-manager@forsharecrm-k8s1
        context:
          cluster: forsharecrm-k8s1
          namespace: default
          user: fs-k8s-app-manager@forsharecrm-k8s1@user
    users:
      - name: fs-k8s-app-manager@forsharecrm-k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@forsharecrm-k8s1
  teleagi-k8s1: |-
    apiVersion: v1
    clusters:
    - cluster:
        certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJME1EY3lOREEyTVRFd05Gb1hEVE0wTURjeU1qQTJNVEV3TkZvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTmhCCnVRdW90SDMrUXcrVVFWOTBLVHU5S0dWUW5kcERoTnpmdHd1cHJSYlJPWTBtOStFMU56YW4yMXJOWDNqMUdqcjEKQWFiWFRCVWZ1cWRidmMramI2Nm5WY04wci9VdnZoN2JRVEt2UFZYR21SbTQ5SVJLUHdzY1NITFVnZ01zd29KNApkdUF3dWJEK2tPMElGZHJtSjVTSktKVng4bmVMZkNEbmdFd1hlRXZja3ZYV3BnNVQ3RXRTTUszUVgzaGNLbmcrCmpJWjJFc0xJejF2d2k5VUZEK3BsUkNQckxaT3RoelN6RUJ1ckVmakxsaDlCdkdoZEU2MUVNdVFTdTYyZXBSL0oKNytIQlQrNUFFb2dHMmhCNTlOdGhlekxGQ3hUK3RTMll5R3FMYi90a0pKdjFpY3ZmdFRkV2xsUUJmS1gra0E4YQpnSXJSVW9YbElPUmNEVHBwR1ZVQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZLYlJXaXlXL3dSK1h6dmgwVXNtQk9ubHBxYjJNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBTlNvM2ZIY1dMYXA4anlZTXNGeQpNNThOM2JRYkh5RWxlaUppT2xkRXpBd3BlUGRjcG1nTGdNbUZaYUZrVjUzVlVZMWU2M2M5c1E3dUhnaUZ3dW1HCnlqZ2wvT0RhN0UzclZCU3hJSVozaXk0SE5MWG1QRTY5Nlh5QkUxN3BTU2Y3ckorQ1Z6RzVmdVZiZ1hKeXM5b0gKdTFKV1hsSVVFcG5PZElON2JQRHFiYW1TRWJtZW5IZW12cTVIYmtoazRoeHF4d01pbkVidU9IazN5YTJ5Nngycgo3Y1BucE53cDBJTGpiaXV0Q21ZT3JXZ1JDNlRzQnB1WUFiNFdnSm44M2YvR2xyV05YdFhJUXBCM2RsR3pzcFd1CmpXNzM3WWV3am41aGx5S1pOMEFIaTFmd0ZyMnZ4cGFMWlZPbGhpVUpSbzVYQ0JSMmRKVnQyRFA1cDgxRG94Qm4KZkdrPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
        server: https://teleagi-k8s1.foneshare.cn:10265
      name: cluster.local
    contexts:
    - context:
        cluster: cluster.local
        user: kubernetes-admin
      name: <EMAIL>
    current-context: <EMAIL>
    kind: Config
    preferences: {}
    users:
    - name: kubernetes-admin
      user:
        token: eyJhbGciOiJSUzI1NiIsImtpZCI6IkRfUEJvVTBwaXFOTEpncldRUVdsSGxURERlbFRpWG11NmRINXc2Q0lTUGMifQ.eyJpc3MiOiJrdWJlcm5ldGVzL3NlcnZpY2VhY2NvdW50Iiwia3ViZXJuZXRlcy5pby9zZXJ2aWNlYWNjb3VudC9uYW1lc3BhY2UiOiJrdWJlLXN5c3RlbSIsImt1YmVybmV0ZXMuaW8vc2VydmljZWFjY291bnQvc2VjcmV0Lm5hbWUiOiJmcy1rOHMtYXBwLW1hbmFnZXIiLCJrdWJlcm5ldGVzLmlvL3NlcnZpY2VhY2NvdW50L3NlcnZpY2UtYWNjb3VudC5uYW1lIjoiZnMtazhzLWFwcC1tYW5hZ2VyIiwia3ViZXJuZXRlcy5pby9zZXJ2aWNlYWNjb3VudC9zZXJ2aWNlLWFjY291bnQudWlkIjoiZjBjZTBjZGMtZGY0Yy00OWY0LTg4NGQtMDdhMTA5NzAyODhjIiwic3ViIjoic3lzdGVtOnNlcnZpY2VhY2NvdW50Omt1YmUtc3lzdGVtOmZzLWs4cy1hcHAtbWFuYWdlciJ9.khwQM6kD8uXvE-tDTsV7qzjrSMC3jf2i92lNCIWoXzA0uNWvZ9eC9JF0WHN_zVt6PVx7PPGh1ypETyNFVbuh0iIPMTCpbtpMTaMGR94A7txwzIJIfljijayKJQjPOp1HqUcUoWQCrlCGt6g3pNLXa3ercgZlJUF_xGQvMPQhO_5Y2qSBuMf1n_Zf0Hjjzm2J40ieqpcEZcxcEC7IlFmN_b7vdJStqLOfjWlYdNJL_x5rblkXpGgVOemPefRRFwcT1mxj4mBhTbD-JQN1heg8ybnnkHJWdayKmNfHPqQIcolHghLpQUmYZterM3JW1AV6KXvZjg0Wrc5zA_DYoeeRtg
  cpgc-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJME1UQXhOVEE0TkRBeE1Wb1hEVE0wTVRBeE16QTROREF4TVZvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBUGo0ClJML0ZHNml6VEREUHo1eXg0OVZyUGQySzlXUGh0dElCUWpoeHIyWFpNRzE1UHdzQ1V3RkhhZ1A4eEJ2NXBmVmEKcmNwVUNRMy92cDNmQ0h6S2dMcXVZTXZGRmVsSU4zOVdlY0dDNVBpdndaSFhxMTlZTDJNS0xRMzJCQkpUdEhQVwp6UXRkcVdJMy9RaXNUQkR6VXZrWnZmcmw4RmhINEFiNERjeWlvZURDbzNtRlVoamlqcGJrazNEV08wOUFYUEJDCmxxM095dEZuZlBncjJZaGZRb2ZRR3YyZzJpSmtiWTd4UVF4MzZLdlg4STdUOGlNam5pRklRalMzMXZkYm5PRmcKRnFOQktzWTZ5N3B0eW1PZ1pEdTcwQlhuMzY1cjhVZ2oxcENKY24yOHoxMTl6a05oVkg2Qm5vNHlqRDVpSGJmSApKcEprdEJKTmluTWpWOHJuMTNFQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZLaTBveHh5UkkwbkdIUFpxWGZRa0dVSmNKcDZNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBUFlGaVNub1J1N0tGVVBScGloSApIeUFPMkxwZDh1NUM4SWZLNENZenluM2c0ZXRJZEE4S2NvdGhKZU5Rb0ZLYjlSbnRBcUpaMllMeTNnNktTMUlFCjlUOGdpNGo3SjhrWG81dG9mNzU5TXlEazBNelVxaUlZcVNlUDNxWEU5N1RSUGh5RDhYaERaVjlrL2U5WEM3ZUEKL1ljYWN3KzBkeGUweVNDL1FWZlhNWWp5Y3BRVDZoaEFtMXpDMlY4VndBR2VVVTI3MittKzlHc1pOeFY5Q000Tgp3bVIwbWpvRHZwa0dIMjBUMUdPb0owVUlwSkRyME0vMWx6SE83ZC9jYzVyRFVjb1ZXNEVxMWpOTHYrU01tT3pXCmJyNFVLRGNSM05ETXh4Wnc1YXE5QWlCektKYkFIaTZFNGp3REpBbktHNUZRRUNwbE5USUNSMzZXUS9XWUZRaFgKQmZZPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          server: https://cpgc-k8s1.foneshare.cn:10293
    contexts:
      - name: fs-k8s-app-manager@k8s1
        context:
          cluster: k8s1
          namespace: default
          user: fs-k8s-app-manager@k8s1@user
    users:
      - name: fs-k8s-app-manager@k8s1@user
        user:
          token: eyJhbGciOiJSUzI1NiIsImtpZCI6IjN0cnotUEoxcmwwcklWb0lsMmZzSXJSLW5RNFJGLUF0VG84V1dleEFDUlUifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.2IVVYNo3Qe0NynWrCUKRn0fwzlL9HUBGuLDm_cDyWK3hYAWNbe9gLIjx5Wn4zKuIIsiKHilCkH9yNXxb6WTCCxd3LofVi2vr8WrG0pmUHUIFmNOI08ipwg9y2VMtZyYICODY0GuIay64_xpVmmm0Is47tvyicLkfWaSzPDnSKtBio_reiNwfNT6CWzsCwGHGZnTLP1CwipuEtqqacW1WdmixfFviMqgVkp7JvQqDiDkCB_n0DEsszZTn-urVstEwknOg8HmJtPXhtertd8kga65VSMwCE_Mt4zMFbUAqeDrKD05Um5HY1vSKX9kbbAnaEREFarazInhCSyjc7g1VNg
    current-context: fs-k8s-app-manager@k8s1
  wingd-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJME1UQXhOekE0TURRMU1Gb1hEVE0wTVRBeE5UQTRNRFExTUZvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBUE1DCnlMc3JjUUFLaU05NVBiS2NBeUNRN3hURnhoaFlldDZVU0Z3RUtyWUd3UVR1SlpKdi9aOUIrRkdWa0MyNzY0V24KWld5b3lNM2MvNWhCOWU5cHNxc1BpWWpUYjBkT1BMZExSY2FxRURFcng4Vkgrc1VTeVVTRW5ZUFV4dWVOOEF5bAoxcGFWUTg1dktleldqNklBZ0xxWThHTnlHZmhQVzk2Z3pmekdqamEvYTEzMzdyNjBkWklOUDRoTU1nWE9pNDJoCkluYU4rV09xdGxXSlMxelBRbkdEbkpHMkxnVW92aXJaZHFybWpaSTVQRDZpOGpBSUFGT1hram10ZjhEQXVjN0MKemg4eGwyREFhR3Q5QWgyK2pzMVh3TmtOTGl2SCtJOVhhOG53UmNzdURNSkdFTG1EY0tyc0pHUC9raG05dHRpMwpVbm9oRFZYUC9mYm9pdi9MTUJFQ0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZCbEk5QnRReEtZOGg2TWpwMUtFdXU3N0hMNGpNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBQzNVeUlTUU1CZVZPOUc3aEhoWAoyUDU2K05mV0UySVk1VHVBREc5aXdIN1VxbFNSaExqakhLTWVzUmtHeGM0V0pjc2RuOUMwWVloUEhrbkVLNStjCkttQlRGT2w1RzB0M2Zub0tXNXkxQWlkQWlWOTVEYzFlYnFDcWxOaHJkYzB2ZHo4UDJrNXZ5UnZwdWNnQ2QzeXYKNFJkNHY3Zjh6NENlMFNKNUttOWFSUXFEZDExQ3k1cGR2a2Jzb1ZZRXhkNTZGWVV2VmoyQTF2ejBabjFoRnkzTQo3TG5kMXNQUjM1RHVxdlh5M3pRdzlTd0JneWVxeG9OWEJNTXVMVEt4SWYvbkI5aDJWV1RGTVZjYU9MeThtRDY4ClpONlZQaE81Q3FvYjQxcnROVkZKNzVPSGVOWVpxYnZCQk1hYnZMTGM4dWc1QUdsejgrQWRKNWVGWVVpVGlzN0oKVFgwPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          server: https://wingd-k8s1.foneshare.cn:10294
    contexts:
      - name: fs-k8s-app-manager@k8s1
        context:
          cluster: k8s1
          namespace: default
          user: fs-k8s-app-manager@k8s1@user
    users:
      - name: fs-k8s-app-manager@k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@k8s1
  kehua-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUMvakNDQWVhZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJME1USXhPVEV5TkRreU4xb1hEVE0wTVRJeE56RXlORGt5TjFvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTXd1CnlOdk9sK3F6TDIwaG5NUlF5WUN3NmJtK3NveTdmdEVSVXZTdmM5dmpVY1dCb3BaQ1RPejRJc1BRZWNTbndKMTEKZEtVeC9Gc2Jvam9jRlhuSlcxTk02RkxiMStDaTFVbS84Ky8zWlR1MktQeFhrZC83Ni9CcDRYelI0R2lXcUhuRQpWdERSSVI4Z2Vudkc3ZDluWW1mQnVSOXN1YjJVVUxjTXhyNXZBZy9VVEx0clF3a1FHTnYzMVg4aXpKbVh4c01rCnh2TWNSdndhV0kvbmJ4NzNzU20wSDAxRE5DamdCMjVGVk9KREU2VXZ5eitwd2s3VjZNNFNrYkVIR0ZlRjNsNUsKY1k0bWhKVHpIcmcxRFRLTXNWdU11d09UNTFKZmJqZW9VWnJ6eHlLNTNnWmRSSlRodmorZGhib1VrdGhybDRoUwpVUFRncEFUZkM4azhOaDZJV284Q0F3RUFBYU5aTUZjd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZNWTluMWNlQzZPdm9tZVFnazFLU2U0eXZydlBNQlVHQTFVZEVRUU8KTUF5Q0NtdDFZbVZ5Ym1WMFpYTXdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBS3k1eWpVdnpKSFNrUTdESVhIKwpkc1VtT0YxYmxDSXFHQWRhRnZjUTQ5UTNmcmNLY0h5WURjTXZGUGVBK0hJSnBSYmR6dGlGNk93OXNGZ05TV3hsCkRHYTM4NWFIWlFqcTJUajMwQ2lFd09MYUdFc0t4WjBITXBXRDg4RUh6WXRGZk9WU3ZvekRiN2VxeTROT2cxSlYKQjh5Y2NwcXBLLzRYSjM5QnAwZjJ6QWlIUGc5MnN0VHRUZWpNblNGa0ZmZFFMNUg3V2NZTW5wN3l4SE9OT3lRRwpVbjdaU3Vsd2I3anJLMTdKS25UdFM2RVJla1RWZGcwV2NySEFGSmtkMFBNQmJrbXpVL252c0ZaOEcwRnVSQlI1Ck03dkc0SjQyNmlpUWFiNG92YWcwZlpEdjJjUU9rbVdhb1JvMVl6ZC9KM1d0ZG1Eb2hWZS9KYzFSTXJCTXhWbVcKQjA4PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          server: https://kehua-k8s1.foneshare.cn:10340
    contexts:
      - name: fs-k8s-app-manager@k8s1
        context:
          cluster: k8s1
          namespace: default
          user: fs-k8s-app-manager@k8s1@user
    users:
      - name: fs-k8s-app-manager@k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@k8s1
  jingbo-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: jingbo-k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUR2VENDQXFXZ0F3SUJBZ0lJQVFiYWo5d0FBS0F3RFFZSktvWklodmNOQVFFTEJRQXdRREVSTUE4R0ExVUUKQ2hNSWFHRnVaM3BvYjNVeEZqQVVCZ05WQkFzVERXRnNhV0poWW1FZ1kyeHZkV1F4RXpBUkJnTlZCQU1UQ210MQpZbVZ5Ym1WMFpYTXdJQmNOTWpVd05ERTJNRGswTnpBd1doZ1BNakExTlRBME1Ea3dPVFV5TWpKYU1FQXhFVEFQCkJnTlZCQW9UQ0doaGJtZDZhRzkxTVJZd0ZBWURWUVFMRXcxaGJHbGlZV0poSUdOc2IzVmtNUk13RVFZRFZRUUQKRXdwcmRXSmxjbTVsZEdWek1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBMmVGTAo5M3FtYzhIU1ZVaUFPcDF4SjBFMzYrUThDcm9IZDRWWFMvUkVTNHZsc1Z0eFZLUm90V2FNUy9FeWJLbHFaenRxClk4Yld3R0lWT2xKKzZlWitkWE11ejk5VXBMc3VHZUxzSW5icCs3TW9uN3pIM1RxZWROOE0rQmZIUFg5MzNlQnEKQlEwaVV2KzEzNDAzU2xqVzBLeFRqUURYNzRpT1FuUThSR28wazJ1VjlaaHhVUEZOS2dtQ1I3YnY0a0hHdWNBaAp3WjNCU002S0JTMk83WXh1cGtrSjlUQ3J3bittZW1wNUZxd2ZrN0c0RVg4cjVrUDJ5Qmk5d01aZFVCcjUrQmhVCnpMYzJlZnF1TWNLS0YxSUVQZmJvdUdlNngvbkN0V3IxSVlqaCtKZnIyTXlpM2Z5WUNabkU0QXI0NGFhOWloQ3kKS0w1anhBZlRZaXQ3aVRMa2tRSURBUUFCbzRHNE1JRzFNQTRHQTFVZER3RUIvd1FFQXdJQ3JEQVBCZ05WSFJNQgpBZjhFQlRBREFRSC9NQjBHQTFVZERnUVdCQlRUVUVQV3ppZlRSN0RINFVlZXpiR3JKSVFjQ0RBOEJnZ3JCZ0VGCkJRY0JBUVF3TUM0d0xBWUlLd1lCQlFVSE1BR0dJR2gwZEhBNkx5OWpaWEowY3k1aFkzTXVZV3hwZVhWdUxtTnYKYlM5dlkzTndNRFVHQTFVZEh3UXVNQ3d3S3FBb29DYUdKR2gwZEhBNkx5OWpaWEowY3k1aFkzTXVZV3hwZVhWdQpMbU52YlM5eWIyOTBMbU55YkRBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQXhIQUFsejlxc21nZng4c2Vnb1pkCkg2dGFrTGZtV2ZwZmZnVTNQTmg5R1JnUjJsR09LZ1ZsNnlPUndEcHhkWjkyL1VEYXZ2SmZsSEU1WG50SEJTUDAKd3d1ZnZONVQyaHZ4R01zSEpDVXJlMTdNb3ZrN3dZSUpHTlZVemxySk5HdUU5MFFZNVk1VjhLWjdtcys1aEpUaAowNHgwSk9SaFR0M3k3NDNGL1pRc3lwR3BHVks3Y3FTVGxEc1F5NzN1Y0gyMjVENHQrNkNSOU9aSGFjbmZNeUpoCk96b1ZkNkFaTVRhZUgyd1JLRzhHL3FhT1Q2UVFjTlRoNEM5dFVYUEJiV0F2TldyWWpidDBaSjBGejJHTWc3MWQKZlB2TEIvaVBRNUhBRGMrODVHUGdkVkVtMll6T2hRZkVlMFRRZW9tMGpvR29EU0NaeDZrQmhkSVRmR1pnN01ucQpZUT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          server: https://***************:6443
    contexts:
      - name: fs-k8s-app-manager@jingbo-k8s1
        context:
          cluster: jingbo-k8s1
          namespace: default
          user: fs-k8s-app-manager@jingbo-k8s1@user
    users:
      - name: fs-k8s-app-manager@jingbo-k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@jingbo-k8s1
  tbea-k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: tbea-k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJQkpON3pPaEVwTzR3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBMU1qWXhNek13TWpsYUZ3MHpOVEExTWpReE16TTFNamxhTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUR5VVdOUjJEV05EMnluRlVQMW0vZDRqSC9kd3hpUDZGS0lIYy9waWxka1hGYThPdkl0czBibzdtcUEKWllJUEFoSWdnWmM1cmJGMnJ5QTREVjA0TGlSQmg3NWhsTXBBUEIrdVVhMlRwZ203cHQvSmROL1lJZ01HcThzNgpzZ0ZiQmRFQ2tSTlBHeWI0b0x6WkZ2MUJSY1FuL0JweEMwWVpIWG1yWTcyMDhyQzQ2VVZJTTJxbnVxaWJzcll0CkNFZzJQaVZ4MGhlSHVhTGhDMk9kM3hRNTNyTGJQY2JSNkowWitpVXRySU1CdkFvR0lSZXFTMkFsMkhtU2huMkkKK25CSHZnM0taU3Q1R1c2V3AvSDNDUUk3bWdIQzVqQlBTV1VlVkd6R3ZZa3NmTGJNS0xWNzYyS2twRmdqaUdrUAo4TWdoMmowZWRDYmJxWnZuZ3lLY2RHaDd3dnFyQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJTTHJYRllNcC94V2ZxT3dVRzZDMmx3ZVBVckhEQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQmtDbTVNcUJQWgpnNFlBRUxRSXFtT0g5Tk42ZDk5K1pVc2RLSXJKMXR0UHl6c3grb0s4dzRXbnJrK1V2VWlvUXhzRVcrbWF0bkFFCjNqeFdCQVIyd1NtQXdacGZnMlJBVFlJVE1NUnp0b0RuVXNhRGhkT3pyL0dMaUl5citDNWZRdWxseDRpRFlhVnEKMHZSL1cyZ1R4M1hDcDRmVjFZM0JVVi9wQkVoOGtaV1JNcUUrZERJMkJUVXFEZk1LZjYvT0h0U3ZwT01xN2hWUwpkWlFIbnJrKzZpZGV2eXh1WXlKQm1rTlFYSVc5TTJhVnEyWTIyWTJWS0tmWWZaMmZ1by90c1htcDg5NGpHbEs0Clo0Y3F4Qk54Y3lWVkcxQUJmOGkvc1VTcUsyUnZub0Jna1REUGN3dWN5NmpUSzhPRVJJdWJpSDJ5SjFaWTRjQTgKMkdmU1V2dm5xUG5ICi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          server: https://tbea-k8s1.foneshare.cn:15512
    contexts:
      - name: fs-k8s-app-manager@tbea-k8s1
        context:
          cluster: tbea-k8s1
          namespace: default
          user: fs-k8s-app-manager@tbea-k8s1@user
    users:
      - name: fs-k8s-app-manager@tbea-k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@tbea-k8s1