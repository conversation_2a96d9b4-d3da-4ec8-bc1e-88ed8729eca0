package job_executor

import (
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/util/docker"
	"fs-k8s-app-manager/service/executor/task_executor"
	"fs-k8s-app-manager/service/job_service"
	"fs-k8s-app-manager/service/notify_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/service/task_service"
	"fs-k8s-app-manager/service/user_service"
	"github.com/elliotchance/pie/v2"
	log "github.com/sirupsen/logrus"
	"strconv"
	"strings"
	"time"
)

func SubmitJob(job models.Job) {
	go execute(job)
}

func execute(job models.Job) {
	defer func() {
		_ = job_service.UpdateEndTime(job.ID, time.Now())
	}()
	if !job.IsWait() {
		//todo: panic error
		return
	}
	if err := waitBeforeExecutionFinish(job, int64(3600)); err != nil {
		log.Warn("cancel job because of before job is not success : " + err.Error())
		_ = job_service.UpdateStatus(job.ID, models.JOB_PHASE_CANCEL)
		if tasks, _ := task_service.FindByJob(job.ID); len(tasks) > 0 {
			for _, task := range tasks {
				if task.IsEnd() {
					continue
				}
				_ = task_service.UpdateStatus(task.ID, models.TASK_PHASE_CANCEL)
			}
		}
		return
	}

	_ = job_service.UpdateStatus(job.ID, models.JOB_PHASE_RUNNING)
	_ = job_service.UpdateStartTime(job.ID, time.Now())
	tasks, err := task_service.FindByJob(job.ID)
	if err != nil || len(tasks) < 1 {
		log.Warn("can't found stages, job id: ", job.ID)
		_ = job_service.UpdateStatus(job.ID, models.JOB_PHASE_FAIL)
		return
	}
	executors := make([]task_executor.TaskExecutor, 0, len(tasks))
	for _, item := range tasks {
		if t, err := creteTask(item); err != nil {
			_ = job_service.UpdateStatus(job.ID, models.JOB_PHASE_FAIL)
			return
		} else {
			executors = append(executors, t)
		}
	}
	var lastTask *task_executor.TaskExecutor
	for _, item := range executors {
		lastTask = &item
		item.Run()
		if !item.GetTask().IsSuccess() {
			break
		}
	}
	if lastTask == nil {
		_ = job_service.UpdateStatus(job.ID, models.JOB_PHASE_FAIL)
		return
	}
	t := (*lastTask).GetTask()
	if t.IsSuccess() || t.IsSkip() {
		_ = job_service.UpdateStatus(job.ID, models.JOB_PHASE_SUCCESS)
	} else if t.IsCancel() {
		_ = job_service.UpdateStatus(job.ID, models.JOB_PHASE_CANCEL)
	} else {
		_ = job_service.UpdateStatus(job.ID, models.JOB_PHASE_FAIL)
	}
	if config.Conf.QiXin.Host != "" {
		go sendJobQiXin(job.ID, executors)
	}
	if config.Conf.FsPaas.Host != "" {
		go sendPaasMessage(job.ID, executors)
	}
	return
}

func sendJobQiXin(jobID uint, executors []task_executor.TaskExecutor) {
	//等待一段时间，确保任务状态已经更新
	time.Sleep(3 * time.Second)
	job, err := job_service.FindById(jobID)
	if err != nil {
		log.Error("can't found job, job id: ", jobID)
		return
	}
	var messages []string
	var app string
	statusIcon := "💡"
	if job.IsSuccess() {
		statusIcon = "✅"
	} else if job.IsFail() {
		statusIcon = "❌"
	}
	for _, item := range executors {
		if v, ok := item.(task_executor.BuildTaskExecutor); ok == true {
			messages = []string{
				fmt.Sprintf("%s%s", statusIcon, job.Type.Desc()+job.Status.Desc()),
				fmt.Sprintf("-----------------------"),
				fmt.Sprintf("【Git仓库】：%s", v.Params.GitUrl),
				fmt.Sprintf("【Maven模块】：%s", v.Params.GitModule),
				fmt.Sprintf("【Git版本】：%s", v.Params.GitTag),
				fmt.Sprintf("【父POM】：%s", v.Params.ParentPom),
				fmt.Sprintf("【Maven镜像】：%s", docker.GetImageSimpleName(v.Params.MavenImage)),
				fmt.Sprintf("【单元测试】：%s", strconv.FormatBool(v.Params.UnitTest)),
				fmt.Sprintf("【强制编译】：%s", strconv.FormatBool(v.Params.ForceCodeCompile)),
				fmt.Sprintf("【镜像版本】：%s", docker.GetDockerImageTag(v.Params.ArtifactImage)),
				fmt.Sprintf("【备注信息】：%s", job.Remark),
				fmt.Sprintf("【任务耗时】：%d 秒", job.TimeCost),
				fmt.Sprintf("【操作人】：%s", job.Author),
			}
			break
		} else if v, ok := item.(task_executor.DeployTaskExecutor); ok == true {
			p, err := pipeline_service.FindById(job.PipelineId)
			if err != nil {
				log.Error("can't found pipeline, pipeline id: ", job.PipelineId)
				return
			}
			app = p.App
			messages = []string{
				fmt.Sprintf("%s%s", statusIcon, job.Type.Desc()+job.Status.Desc()),
				fmt.Sprintf("-----------------------"),
				fmt.Sprintf("【应用】：%s", p.App),
				fmt.Sprintf("【环境】：%s", p.Namespace),
				fmt.Sprintf("【所在集群】：%s", p.Cluster),
				fmt.Sprintf("【基础镜像】：%s", docker.GetImageSimpleName(p.BaseImage)),
				fmt.Sprintf("【发布版本】：%s", v.Params.DeployModules.Version()),
				fmt.Sprintf("【发布备注】：%s", job.Remark),
				fmt.Sprintf("【发布耗时】：%d 秒", job.TimeCost),
				fmt.Sprintf("【操作人】：%s", job.Author),
			}
			if job.IsFail() {
				messages = append(messages, fmt.Sprintf("【小小提示】：%s", "请通过实例事件和Tomcat启动日志来查看失败原因"))
			}
			if job.TimeCost > 300 {
				messages = append(messages, fmt.Sprintf("【小小提示】：%s", "可以通过参数[发版并发数]来加快发布时间"))
			}
			break
		}
	}
	if len(messages) == 0 {
		log.Error("can't found messages for qixin")
		return
	}
	user, err := user_service.FindByRealName(job.Author)
	if err != nil {
		log.Error("can't found user, real name: ", job.Author)
		return
	}
	userIds := make([]int, 0, 5)
	userIds = append(userIds, user.EmployeeId)
	if app != "" {
		//todo: 为了避免应用在集群迁移过程中的干扰， 暂且关闭。 迁移完以后再开启
		//ids := cmdb_service.GetOwnerIds(app)
		//userIds = append(userIds, ids...)
	}
	//如果userid重复，则会重复发送
	userIds = pie.Unique(userIds)
	if err := notify_service.SendQiXinToPublishSession(userIds, strings.Join(messages, "\n")); err != nil {
		log.Error("send qixin message fail: ", err)
	}
}

func sendPaasMessage(jobID uint, executors []task_executor.TaskExecutor) {
	//等待一段时间，确保任务状态已经更新
	time.Sleep(3 * time.Second)
	job, err := job_service.FindById(jobID)
	if err != nil {
		log.Error("can't found job, job id: ", jobID)
		return
	}

	conf := config.Conf.FsPaas
	ownerId := conf.SysUserId
	if user, err := user_service.FindByRealName(job.Author); err != nil {
		log.Error("can't found user, real name: ", job.Author)
	} else {
		ownerId = user.EmployeeId
	}
	for _, item := range executors {
		if v, ok := item.(task_executor.DeployTaskExecutor); ok == true {
			pipe, err := pipeline_service.FindById(v.Params.PipelineId)
			if err != nil {
				log.Error("can't found pipeline, pipeline id: ", v.Params.PipelineId)
				return
			}
			p := notify_service.PaasMessageParam{
				OwnerId:             ownerId,
				SysUserId:           conf.SysUserId,
				ObjectId:            conf.ObjectId,
				StartTime:           job.CreatedAt.Time().UnixNano() / 1e6,
				EndTime:             job.UpdatedAt.Time().UnixNano() / 1e6,
				QiXinSessionFieldId: conf.QiXinSessionFieldId,
				App:                 job.App,
				Tag:                 v.Params.DeployModules.Version(),
				Cluster:             pipe.Cluster,
				Namespace:           pipe.Namespace,
				DeployDetailLink:    fmt.Sprintf("%s/#/cicd/app-deploy-detail?jobId=%d", config.Conf.K8sAppManager.Host, job.ID),
				PublishStatus:       "未知状态",
				PublishRemark:       v.Params.Remark,
				BuildTimeCost:       0,
				DeployTimeCost:      job.TimeCost,
			}
			if job.IsSuccess() {
				p.PublishStatus = "发布成功"
			} else if job.IsFail() {
				p.PublishStatus = "发布失败"
			} else if job.IsCancel() {
				p.PublishStatus = "发布取消"
			}
			err = notify_service.SendPaasMessage(p)
			if err != nil {
				log.Error("send paas message fail: ", err)
			}
			break
		}
	}
}

func waitBeforeExecutionFinish(job models.Job, waitTimeoutSec int64) error {
	startTime := time.Now().Unix()
	if job.BeforeJobId != 0 {
		for {
			if (time.Now().Unix() - startTime) > waitTimeoutSec {
				return errors.New("before job wait timeout")
			}
			before, _ := job_service.FindById(job.BeforeJobId)
			if before.IsEnd() {
				if before.IsSuccess() {
					return nil
				} else {
					return fmt.Errorf("before job fail, status: %s", before.Status)
				}
			}
			time.Sleep(5 * time.Second)
		}
	}
	return nil
}

func creteTask(t models.Task) (task_executor.TaskExecutor, error) {
	var ret task_executor.TaskExecutor
	var err error
	if t.Type == models.TASK_TYPE_BUILD {
		ret, err = task_executor.BuildTaskExecutor{}.Build(&t)
	} else if t.Type == models.TASK_TYPE_DEPLOY {
		ret, err = task_executor.DeployTaskExecutor{}.Build(&t)
	} else if t.Type == models.TASK_TYPE_WEBHOOK {
		ret, err = task_executor.WebhookTaskExecutor{}.Build(&t)
	} else if t.Type == models.TASK_TYPE_EOLINKER {
		ret, err = task_executor.EolinkerTaskExecutor{}.Build(&t)
	} else {
		err = errors.New(fmt.Sprintf("unknown stage type: %s", t.Type))
	}
	return ret, err
}
