package k8s_service

import (
	"bytes"
	"errors"
	"fmt"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/util/docker"
	"fs-k8s-app-manager/pkg/util/strslice"
	"io"
	"os"
	"sort"
	"strings"
	"time"

	log "github.com/sirupsen/logrus"
	appV1 "k8s.io/api/apps/v1"
	autoScaleV1 "k8s.io/api/autoscaling/v1"
	corev1 "k8s.io/api/core/v1"
	errors2 "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/reference"
)

func ListPod(cluster, namespace, app string) (ret []dto.Pod, err error) {
	podList, err := k8s.GetPodList(cluster, namespace, app)
	if err != nil {
		return
	}

	for _, item := range podList.Items {
		d := parsePodDTO(cluster, &item)
		d.Deregister = false
		ret = append(ret, *d)
	}

	dePods := ListDeregister(cluster, namespace, app, false)
	ret = append(ret, dePods...)

	ret = podFilter(ret, func(p dto.Pod) bool {
		return !strings.EqualFold(p.StatusReason, "Evicted")
	})
	return
}

func DeleteDeregisterPodCache(cluster, namespace, app string) {
	cacheKey := key.Pre().POD.Key(fmt.Sprintf("DEREGISTER#%s#%s#%s", cluster, namespace, app))
	cache.Delete(cacheKey)
}
func ListDeregister(cluster, namespace, app string, useCache bool) []dto.Pod {
	//获取摘除掉的pod
	cacheKey := key.Pre().POD.Key(fmt.Sprintf("DEREGISTER#%s#%s#%s", cluster, namespace, app))
	if useCache {
		if data, found := cache.GetStruct(cacheKey, []dto.Pod{}); found {
			return data
		}
	}
	ret := make([]dto.Pod, 0, 5)
	if cluster != "" && namespace != "" && app != "" {
		dePods, err := k8s.GetPodList(cluster, namespace, app+"-close")
		if err == nil {
			for _, item := range dePods.Items {
				d := parsePodDTO(cluster, &item)
				d.Deregister = true
				ret = append(ret, *d)
			}
		}
	}
	_ = cache.SetStruct(key.Pre().POD.Key(cacheKey), ret, 4*time.Hour)
	return ret
}

func GetPodByIP(cluster, namespace, ip string) (ret dto.Pod, err error) {
	podList, err := k8s.ListPodByIP(cluster, namespace, ip)
	if err != nil {
		return
	}
	if podList == nil || len(podList.Items) == 0 {
		err = fmt.Errorf("can't found pod, %s/%s/%s", cluster, namespace, ip)
		return
	}

	for _, item := range podList.Items {
		//返回第一个
		ret = *parsePodDTO(cluster, &item)
		break
	}
	return
}

func podFilter(s []dto.Pod, filter func(podDTO dto.Pod) bool) []dto.Pod {
	ret := s[:0]
	for _, item := range s {
		if filter(item) {
			ret = append(ret, item)
		}
	}
	return ret
}
func PodEventDTOs(cluster, namespace, pod string, limit uint) (ret []dto.Event, err error) {
	ret = make([]dto.Event, 0, limit)
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	events, err := podEvents(c, namespace, pod, 50)
	if err != nil {
		return ret, err
	}

	if len(events) > int(limit) {
		events = events[:limit]
	}

	for _, e := range events {
		eventDto := parseEventDTO(&e)
		ret = append(ret, eventDto)
	}
	return ret, nil
}
func ListDeployment(cluster, namespace string) (ret []dto.Deployment, err error) {
	if deps, err := k8s.GetDeploymentList(cluster, namespace); err == nil {
		for _, item := range deps.Items {
			ret = append(ret, *ParseDeploymentDTO(&item, cluster))
		}
	}
	if ret == nil {
		ret = make([]dto.Deployment, 0)
	}
	return
}
func ListNode(cluster string) (ret []dto.Node, err error) {
	if items, err := k8s.GetNodeList(cluster); err == nil {
		for _, item := range items.Items {
			ret = append(ret, *parseNodeDTO(&item))
		}
	}
	return
}
func ListService(cluster, namespace string) (ret []dto.Service, err error) {
	if items, err := k8s.GetServiceList(cluster, namespace); err == nil {
		ret = make([]dto.Service, 0, len(items.Items))
		for _, item := range items.Items {
			ret = append(ret, parseServiceDTO(&item))
		}
	}
	return
}
func ListReplicaSet(cluster, namespace string) (ret []dto.ReplicaSet, err error) {
	if items, err := k8s.GetReplicaSetList(cluster, namespace); err == nil {
		ret = make([]dto.ReplicaSet, 0, len(items.Items))
		for _, item := range items.Items {
			ret = append(ret, *ParseReplicaSetDTO(&item))
		}
	}
	return
}

func ListAppReplicaSet(cluster, namespace, app string) (ret *appV1.ReplicaSetList, err error) {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	selector := labels.SelectorFromSet(map[string]string{"app": app}).String()
	return c.AppsV1().ReplicaSets(namespace).List(metav1.ListOptions{LabelSelector: selector})
}

func ListAppReplicaSetDTO(cluster, namespace, app string) (ret []dto.ReplicaSet, err error) {
	items, err := ListAppReplicaSet(cluster, namespace, app)
	if err != nil {
		return
	}
	for _, item := range items.Items {
		ret = append(ret, *ParseReplicaSetDTO(&item))
	}
	return
}

func ListPodFiles(cluster, namespace, pod, path string) (*[]dto.PodFile, error) {
	return kubectl.ListPodFile(cluster, namespace, pod, path)
}
func ListJavaCmsFile(cluster, namespace, pod string) ([]string, error) {
	return kubectl.ListJavaCmsFile(cluster, namespace, pod)
}

func CopyFileFromPod(cluster, namespace, pod, srcFile, destFile string) error {
	return kubectl.CopyFileFromPod(cluster, namespace, pod, srcFile, destFile)
}

func ArchiveFiles(cluster, namespace, pod, dir string, files []string, destFile string) error {
	return kubectl.ArchiveFiles(cluster, namespace, pod, dir, files, destFile)
}

func CopyFileToPod(cluster, namespace, pod, tgtFile, srcFile string) (err error) {
	err = kubectl.CopyFileToPod(cluster, namespace, pod, tgtFile, srcFile)
	return
}

func GetPod(cluster, namespace, pod string) (ret *dto.Pod, err error) {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	if p, err := c.CoreV1().Pods(namespace).Get(pod, metav1.GetOptions{}); err == nil {
		ret = parsePodDTO(cluster, p)
	}
	return
}

func PodDetail(cluster, namespace, pod string) (ret *corev1.Pod, err error) {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	return c.CoreV1().Pods(namespace).Get(pod, metav1.GetOptions{})
}

func PodLog(cluster, namespace, pod, container string, tailLines int64, previous bool) (ret string, err error) {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return
	}

	option := corev1.PodLogOptions{
		Container: container,
		TailLines: &tailLines,
		Previous:  previous,
	}
	req := c.CoreV1().Pods(namespace).GetLogs(pod, &option)
	podLogs, err := req.Stream()
	if err != nil {
		if strings.Contains(err.Error(), "previous terminated container") {
			return "pod never restart", nil
		}
		return
	}
	defer podLogs.Close()
	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, podLogs)
	if err != nil {
		return
	}
	ret = buf.String()
	return
}

//千万注意： 这个扩缩容操作有坑。 使用该方法进行扩缩容时，有可能会导致当前所有pod重启，造成服务中断。具体原因不清楚，请不要使用该方法。
//func AppScale(cluster, namespace, app string, replicas int32) error {
//	c, err := k8s.GetKubernetesClient(cluster)
//	if err != nil {
//		return err
//	}
//	dep, err := c.AppsV1().Deployments(namespace).Get(app, metav1.GetOptions{})
//	if err != nil {
//		return err
//	}
//	if *dep.Spec.Replicas == replicas {
//		return nil
//	}
//
//	dep.Spec.Replicas = &replicas
//	_, err = c.AppsV1().Deployments(namespace).Update(dep)
//	return err
//}

func AppRedeploy(cluster, namespace, app string, maxSurge string) error {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return err
	}
	dep, err := c.AppsV1().Deployments(namespace).Get(app, metav1.GetOptions{})
	if err != nil {
		return err
	}
	if maxSurge != "" && dep.Spec.Strategy.Type == appV1.RollingUpdateDeploymentStrategyType {
		dep.Spec.Strategy.RollingUpdate.MaxSurge.Type = intstr.String
		dep.Spec.Strategy.RollingUpdate.MaxSurge.StrVal = maxSurge
		dep.Spec.Strategy.RollingUpdate.MaxUnavailable.IntVal = 0
	}
	dep.Spec.Template.Annotations["fxiaoke.com/redeploy"] = time.Now().Format("2006-01-02 15:04:05")
	_, err = c.AppsV1().Deployments(namespace).Update(dep)
	return err
}

func UpdateDeployStrategyToRecreate(cluster, namespace, app string) error {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return err
	}
	dep, err := c.AppsV1().Deployments(namespace).Get(app, metav1.GetOptions{})
	if err != nil {
		return err
	}
	dep.Spec.Strategy.Type = appV1.RecreateDeploymentStrategyType
	dep.Spec.Strategy.RollingUpdate = nil
	dep.Spec.Template.Annotations["fxiaoke.com/redeploy"] = time.Now().Format("2006-01-02 15:04:05")
	_, err = c.AppsV1().Deployments(namespace).Update(dep)
	return err
}

func UpdateDeploymentResource(cluster, namespace, app string, limitCPU float64, limitMemory int64, requestCPU float64, requestMemory int64) error {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return err
	}
	dep, err := c.AppsV1().Deployments(namespace).Get(app, metav1.GetOptions{})
	if err != nil {
		return err
	}
	dep.Spec.Template.Spec.Containers[0].Resources.Limits["cpu"] = resource.MustParse(fmt.Sprintf("%dm", int64(limitCPU*1000)))
	dep.Spec.Template.Spec.Containers[0].Resources.Limits["memory"] = resource.MustParse(fmt.Sprintf("%dMi", limitMemory))
	dep.Spec.Template.Spec.Containers[0].Resources.Requests["cpu"] = resource.MustParse(fmt.Sprintf("%dm", int64(requestCPU*1000)))
	dep.Spec.Template.Spec.Containers[0].Resources.Requests["memory"] = resource.MustParse(fmt.Sprintf("%dMi", requestMemory))
	_, err = c.AppsV1().Deployments(namespace).Update(dep)
	return err
}

func Rollout(cluster, namespace, app, revision string) error {
	return kubectl.RolloutDeployment(cluster, namespace, app, revision)
}

func PodDelete(cluster, namespace, pod string) error {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return err
	}
	err = c.CoreV1().Pods(namespace).Delete(pod, &metav1.DeleteOptions{})
	return err
}
func PodDeregister(cluster, namespace, pod string) error {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return err
	}
	podObj, err := c.CoreV1().Pods(namespace).Get(pod, metav1.GetOptions{})
	if err != nil {
		return err
	}
	appLabel, found := podObj.Labels["app"]
	if !found {
		return errors.New("pod have not app label")
	}
	if strings.HasSuffix(appLabel, "-close") {
		return errors.New("实例已经被摘掉")
	}

	_, err = c.CoreV1().Pods(namespace).Patch(pod, types.StrategicMergePatchType,
		[]byte(fmt.Sprintf(`{"metadata": {"labels": {"app": "%s"}}}`, appLabel+"-close")))
	return err
}

func PodVersionRetain(cluster, namespace, pod string) error {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return err
	}
	podObj, err := c.CoreV1().Pods(namespace).Get(pod, metav1.GetOptions{})
	if err != nil {
		return err
	}
	versionLabel, found := podObj.Labels["version"]
	if !found {
		return errors.New("pod have not version label")
	}
	if strings.HasSuffix(versionLabel, "-retain") {
		return errors.New("实例版本已经被保留")
	}
	_, err = c.CoreV1().Pods(namespace).Patch(pod, types.StrategicMergePatchType,
		[]byte(fmt.Sprintf(`{"metadata": {"labels": {"version": "%s"}}}`, versionLabel+"-retain")))
	return err
}

func DeploymentDetail(cluster, namespace, name string) (ret *appV1.Deployment, err error) {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	return c.AppsV1().Deployments(namespace).Get(name, metav1.GetOptions{})
}

func GetDeploymentDTO(cluster, namespace, name string) (ret *dto.Deployment, err error) {
	dep, err := DeploymentDetail(cluster, namespace, name)
	if err != nil {
		return
	}
	ret = ParseDeploymentDTO(dep, cluster)
	return
}
func IngressExist(cluster, namespace, name string) (bool, error) {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return false, err
	}

	ingress, err := c.ExtensionsV1beta1().Ingresses(namespace).Get(name, metav1.GetOptions{})
	if err != nil {
		if errors2.IsNotFound(err) {
			err = nil
		}
		return false, err
	}
	return ingress != nil, nil
}

func ServiceDetail(cluster, namespace, name string) (ret *corev1.Service, err error) {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	ret, err = c.CoreV1().Services(namespace).Get(name, metav1.GetOptions{})
	return
}

func ServiceDTO(cluster, namespace, name string) (*dto.Service, error) {
	s, err := ServiceDetail(cluster, namespace, name)
	if errors2.IsNotFound(err) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	ret := parseServiceDTO(s)
	return &ret, nil

}

// event数据获取耗时比较长，非必要场景尽量不要加载event数据，必要场景可采用异步的懒加载模式
func podEvents(c *kubernetes.Clientset, namespace, pod string, limit int) (ret []corev1.Event, err error) {
	listOptions := metav1.ListOptions{
		FieldSelector: fmt.Sprintf("involvedObject.kind=Pod,involvedObject.name=%s", pod),
	}
	if events, err := c.CoreV1().Events(namespace).List(listOptions); err == nil {
		ret = events.Items
		if len(ret) > limit {
			ret = ret[:limit]
		}
		sort.Slice(ret, func(i, j int) bool {
			return ret[i].LastTimestamp.Time.After(ret[j].LastTimestamp.Time)
		})
	}
	return
}

func parseServiceDTO(s *corev1.Service) dto.Service {
	ports := make([]dto.ServicePort, 0, len(s.Spec.Ports))
	for _, item := range s.Spec.Ports {
		ports = append(ports, dto.ServicePort{
			Name:       item.Name,
			Protocol:   string(item.Protocol),
			Port:       item.Port,
			TargetPort: item.TargetPort,
			NodePort:   item.NodePort,
		})
	}

	return dto.Service{
		Name:      s.Name,
		Namespace: s.Namespace,
		Ports:     ports,
	}
}

func parseEventDTO(e *corev1.Event) dto.Event {
	ret := dto.Event{
		Name:               e.Name,
		Namespace:          e.Namespace,
		InvolvedObjectType: e.InvolvedObject.Kind,
		InvolvedObjectName: e.InvolvedObject.Name,
		Type:               e.Type,
		Reason:             e.Reason,
		Message:            e.Message,
		Action:             e.Action,
		Count:              e.Count,
		CreateTime:         dto.NewTimeWithFormatter(e.CreationTimestamp.Time, "2006-01-02 15:04:05"),
		EventTime:          dto.NewTimeWithFormatter(e.EventTime.Time, "2006-01-02 15:04:05"),
		FirstTime:          dto.NewTimeWithFormatter(e.FirstTimestamp.Time, "2006-01-02 15:04:05"),
		LastTime:           dto.NewTimeWithFormatter(e.LastTimestamp.Time, "2006-01-02 15:04:05"),
	}
	extMsg := ""
	if ret.Reason == "ContainerKilled" {
		extMsg = "容器被强制Kill掉了。可能情况： 1）实例或者所在宿主机CPU负载高导致健康检查失败 2)实例从宿主机上被驱逐 3）其他"
	} else if ret.Reason == "ContainerOOM" {
		extMsg = "容器发生了OOM。可能情况： 1）程序内存有泄漏 2）分配的容器内存不够用 3）其他"
	} else if ret.Reason == "JvmOOM" {
		extMsg = "Java进程发生了OOM,可在日志目录里下载dump文件。可能情况： 1）程序内存有泄漏 2）分配的Java堆内存不够用 3）其他"
	} else if ret.Reason == "FailedScheduling" && strings.Contains(ret.Message, "nodes are available") {
		extMsg = "无法调度到宿主机。可能情况： 1）配置的请求资源太高？ 2）k8s资源池资源不够，联系管理员 3）其他"
	} else if ret.Reason == "StdoutLogBigSize" {
		extMsg = "太多的日志被写入到标准输出。可能情况：1）日志配置文件（比如：logback.xml）配置了Console Appender"
	} else if ret.Reason == "Unhealthy" && strings.Contains(ret.Message, "Liveness probe failed") {
		extMsg = "存活健康检测失败。可能原因：1）实例负载很高 2）内存GC时间很长 3）实例正在启动中 4)实例出现其他问题"
	} else if ret.Reason == "Failed" && (strings.Contains(ret.Message, "ErrImagePull") || strings.Contains(ret.Message, "ImagePullBackOff")) {
		extMsg = "容器镜像拉取失败。可能原因：1）发版时跳过了镜像 2）镜像已经被GC掉"
	}
	if extMsg != "" {
		ret.Message = fmt.Sprintf("%s -- %s", ret.Message, extMsg)
	}
	return ret
}

// parsePodDTO
// pod生命周描述文档：https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/
func parsePodDTO(cluster string, pod *corev1.Pod) *dto.Pod {
	versionRetain := strings.HasSuffix(pod.Labels["version"], "-retain")
	ret := dto.Pod{
		Name:               pod.Name,
		Cluster:            cluster,
		Namespace:          pod.Namespace,
		PodIP:              pod.Status.PodIP,
		HostIP:             pod.Status.HostIP,
		NodeName:           pod.Spec.NodeName,
		LabelApp:           pod.Labels["app"],
		CreateTime:         dto.NewTime(pod.CreationTimestamp.Time),
		VersionRetain:      versionRetain,
		Phase:              string(pod.Status.Phase),
		StatusReason:       pod.Status.Reason,
		InitContainersName: make([]string, 0, 5),
	}
	for _, it := range pod.Status.Conditions {
		if it.Type == corev1.PodReady {
			ret.Ready = it.Status == corev1.ConditionTrue
			continue
		}
	}
	if len(pod.Spec.InitContainers) > 0 {
		for _, it := range pod.Spec.InitContainers {
			ret.InitContainersName = append(ret.InitContainersName, it.Name)
		}
	}

	if len(pod.Spec.Containers) > 0 {
		container0 := pod.Spec.Containers[0]
		ret.Container0Name = container0.Name
		ret.Container0Image = container0.Image
		ret.Container0ImageTag = docker.GetDockerImageTag(container0.Image)
		ret.RequestMemory = container0.Resources.Requests.Memory().Value()
		ret.RequestCPU = container0.Resources.Requests.Cpu().MilliValue()
		ret.LimitMemory = container0.Resources.Limits.Memory().Value()
		ret.LimitCPU = container0.Resources.Limits.Cpu().MilliValue()

		ret.DeployTag = docker.DockerTag2GitTag(ret.Container0ImageTag)
		//如果有部署模块变量，则从变量里获取部署tag
		if v, mods, err := GetDeployTag(pod); err == nil {
			ret.DeployTag = v
			ret.DeployModules = dto.ToDeployModules(mods)
		} else {
			log.Warn(err.Error())
		}
	}

	if pod.Status.StartTime != nil {
		ret.StartTime = dto.NewTime(pod.Status.StartTime.Time)
	}

	if pod.DeletionTimestamp != nil {
		ret.DeleteTime = dto.NewTime(pod.DeletionTimestamp.Time)
	}

	if len(pod.Status.ContainerStatuses) > 0 {
		containerStatus := pod.Status.ContainerStatuses[0]
		ret.Container0Ready = containerStatus.Ready
		ret.RestartCount = containerStatus.RestartCount
		if containerStatus.LastTerminationState.Terminated != nil {
			ret.RestartTime = dto.NewTime(containerStatus.LastTerminationState.Terminated.FinishedAt.Time)
			ret.RestartCode = containerStatus.LastTerminationState.Terminated.ExitCode
			ret.RestartReason = containerStatus.LastTerminationState.Terminated.Reason
		}

		if containerStatus.State.Terminated != nil {
			ret.Container0Status = "Terminated"
		} else if containerStatus.State.Running != nil {
			ret.Container0Status = "Running"
		} else {
			ret.Container0Status = "Waiting"
		}
	}
	ret.StatusDesc = buildStatusDesc(ret)
	return &ret
}

func buildStatusDesc(p dto.Pod) string {
	if !p.DeleteTime.IsZero() {
		return "关闭中"
	} else if p.Phase == string(corev1.PodPending) {
		if p.StartTime.IsZero() {
			return "调度中"
		} else {
			return "准备中"
		}
	} else if p.Phase == string(corev1.PodRunning) {
		if p.Ready && p.Container0Status == "Running" {
			return "运行中"
		} else if p.Container0Status == "Terminated" {
			return "容器关闭中"
		} else {
			//不太好区分是刚启动还是因为其他原因导致的NotReady，暂且通过最后启动时间来实现简单的判断
			if time.Now().Sub(p.LastStartTime()).Seconds() > 1800 {
				return "不健康"
			}
			return "启动中"
		}
	} else if p.Phase == string(corev1.PodSucceeded) || p.Phase == string(corev1.PodFailed) {
		return "已关闭"
	} else if p.Phase == string(corev1.PodUnknown) {
		return "未知状态"
	} else {
		return "未知状态"
	}
}

func CetEnvValue(envs []corev1.EnvVar, name string) string {
	for _, it := range envs {
		if it.Name == name {
			return it.Value
		}
	}
	return ""
}

func GetDeployTag(obj interface{}) (string, datatype.DeployModules, error) {
	modules, err := GetDeployModules(obj)
	if err != nil {
		return "", nil, errors.New(fmt.Sprintf("get deploy modules error: %s", err.Error()))
	}
	return modules.Version(), modules, nil
}

func GetDeployModules(obj interface{}) (datatype.DeployModules, error) {
	var initContainers []corev1.Container
	switch o := obj.(type) {
	case *corev1.Pod:
		initContainers = o.Spec.InitContainers
	case corev1.Pod:
		initContainers = o.Spec.InitContainers
	case *appV1.ReplicaSet:
		initContainers = o.Spec.Template.Spec.InitContainers
	case appV1.ReplicaSet:
		initContainers = o.Spec.Template.Spec.InitContainers
	case *appV1.Deployment:
		initContainers = o.Spec.Template.Spec.InitContainers
	case appV1.Deployment:
		initContainers = o.Spec.Template.Spec.InitContainers
	}
	//如果是init container模式
	if len(initContainers) > 0 {
		return getDeployModulesFromInitContainer(initContainers)
	}

	//如果是war仓库模式 （历史遗留问题，war仓库模式的部署模块变量是放在容器环境变量里的）
	var envs []corev1.EnvVar
	switch o := obj.(type) {
	case *corev1.Pod:
		envs = o.Spec.Containers[0].Env
	case *appV1.ReplicaSet:
		envs = o.Spec.Template.Spec.Containers[0].Env
	case *appV1.Deployment:
		envs = o.Spec.Template.Spec.Containers[0].Env
	}

	return getDeployModulesFromEnv(envs)
}

func getDeployModulesFromInitContainer(initContainers []corev1.Container) (datatype.DeployModules, error) {
	ret := datatype.DeployModules{}
	for _, c := range initContainers {
		url := CetEnvValue(c.Env, "GIT_URL")
		tag := CetEnvValue(c.Env, "GIT_TAG")
		if url != "" && tag != "" {
			ret = append(ret, datatype.DeployModule{
				GitUrl:          url,
				Module:          CetEnvValue(c.Env, "GIT_MODULE"),
				ContextPath:     CetEnvValue(c.Env, "CONTEXT_PATH"),
				CommitID:        CetEnvValue(c.Env, "COMMIT_ID"),
				Tag:             tag,
				ArtifactImage:   c.Image,
				ArtifactPathSrc: CetEnvValue(c.Env, "ARTIFACT_PATH_SRC"),
				ArtifactPathDst: CetEnvValue(c.Env, "ARTIFACT_PATH_DST"),
			})
		}
	}
	return ret, nil
}

func getDeployModulesFromEnv(envs []corev1.EnvVar) (datatype.DeployModules, error) {
	ret := datatype.DeployModules{}
	if v := CetEnvValue(envs, "FS_DEPLOY_MODULES"); v != "" {
		err := ret.FromJson(v)
		return ret, err
	}
	return ret, errors.New("deploy modules not found")
}

func ParseDeploymentDTO(obj *appV1.Deployment, cluster string) *dto.Deployment {
	ret := &dto.Deployment{
		Cluster:            cluster,
		Name:               obj.Name,
		Namespace:          obj.Namespace,
		Replicas:           *obj.Spec.Replicas,
		Container0Name:     obj.Spec.Template.Spec.Containers[0].Name,
		Container0Image:    obj.Spec.Template.Spec.Containers[0].Image,
		Container0ImageTag: docker.GetDockerImageTag(obj.Spec.Template.Spec.Containers[0].Image),
		RequestMemory:      obj.Spec.Template.Spec.Containers[0].Resources.Requests.Memory().Value(),
		RequestCPU:         obj.Spec.Template.Spec.Containers[0].Resources.Requests.Cpu().MilliValue(),
		LimitMemory:        obj.Spec.Template.Spec.Containers[0].Resources.Limits.Memory().Value(),
		LimitCPU:           obj.Spec.Template.Spec.Containers[0].Resources.Limits.Cpu().MilliValue(),
		CreateTime:         dto.NewTime(obj.CreationTimestamp.Time),
		Revision:           obj.Annotations["deployment.kubernetes.io/revision"],
		Language:           obj.Annotations["fxiaoke.com/language"],
		LastModifyUser:     obj.Annotations["fxiaoke.com/last-modify-user"],
		DeployUser:         obj.Annotations["fxiaoke.com/deploy-user"],
		DeployTime:         obj.Annotations["fxiaoke.com/deploy-time"],
		DeployRemark:       obj.Annotations["fxiaoke.com/deploy-remark"],
	}

	ret.DeployTag = docker.DockerTag2GitTag(ret.Container0ImageTag)
	//如果有部署模块变量，则从变量里获取部署tag
	if v, mods, err := GetDeployTag(obj); err == nil {
		ret.DeployTag = v
		ret.DeployModules = dto.ToDeployModules(mods)
	} else {
		log.Error(err.Error())
	}
	for _, it := range obj.Status.Conditions {
		if it.Reason == "NewReplicaSetAvailable" {
			ret.UpdateTime = dto.NewTime(it.LastUpdateTime.Time)
			break
		}
	}

	return ret
}

func parseNodeDTO(obj *corev1.Node) *dto.Node {
	ret := &dto.Node{
		Name:                    obj.Name,
		Schedulable:             !obj.Spec.Unschedulable,
		Arch:                    obj.Status.NodeInfo.Architecture,
		Os:                      obj.Status.NodeInfo.OperatingSystem,
		ContainerRuntimeVersion: obj.Status.NodeInfo.ContainerRuntimeVersion,
		KernelVersion:           obj.Status.NodeInfo.KernelVersion,
		OsImage:                 obj.Status.NodeInfo.OSImage,
		CPUCapacity:             obj.Status.Capacity.Cpu().MilliValue(),
		MemoryCapacity:          obj.Status.Capacity.Memory().ScaledValue(resource.Mega),
	}
	obj.Status.Capacity.Cpu().MilliValue()

	for k, v := range obj.Labels {
		if k == "fxiaoke.com/dedicated" {
			ret.DedicatedName = v
		}
	}
	return ret
}

func ParseReplicaSetDTO(obj *appV1.ReplicaSet) *dto.ReplicaSet {
	ret := &dto.ReplicaSet{
		Deployment: dto.Deployment{
			Name:               obj.Name,
			Namespace:          obj.Namespace,
			Replicas:           *obj.Spec.Replicas,
			Container0Name:     obj.Spec.Template.Spec.Containers[0].Name,
			Container0Image:    obj.Spec.Template.Spec.Containers[0].Image,
			Container0ImageTag: docker.GetDockerImageTag(obj.Spec.Template.Spec.Containers[0].Image),
			RequestMemory:      obj.Spec.Template.Spec.Containers[0].Resources.Requests.Memory().Value(),
			RequestCPU:         obj.Spec.Template.Spec.Containers[0].Resources.Requests.Cpu().MilliValue(),
			LimitMemory:        obj.Spec.Template.Spec.Containers[0].Resources.Limits.Memory().Value(),
			LimitCPU:           obj.Spec.Template.Spec.Containers[0].Resources.Limits.Cpu().MilliValue(),
			CreateTime:         dto.NewTime(obj.CreationTimestamp.Time),
			Revision:           obj.Annotations["deployment.kubernetes.io/revision"],
			Language:           obj.Annotations["fxiaoke.com/language"],
			LastModifyUser:     obj.Annotations["fxiaoke.com/last-modify-user"],
			DeployUser:         obj.Annotations["fxiaoke.com/deploy-user"],
			DeployTime:         obj.Annotations["fxiaoke.com/deploy-time"],
			DeployRemark:       obj.Annotations["fxiaoke.com/deploy-remark"],
		},
	}
	ret.DeployTag = docker.DockerTag2GitTag(ret.Container0ImageTag)
	//如果有部署模块变量，则从变量里获取部署tag
	if v, mods, err := GetDeployTag(obj); err == nil {
		ret.DeployTag = v
		ret.DeployModules = dto.ToDeployModules(mods)
	} else {
		log.Error(err.Error())
	}

	return ret
}

func ListEvent(cluster, namespace, eventType, reason string) (ret []dto.Event, err error) {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return
	}
	fieldMap := make(map[string]string)

	if len(eventType) > 0 {
		fieldMap["type"] = eventType
	}
	if len(reason) > 0 {
		fieldMap["reason"] = reason
	}

	fieldSelector := labels.SelectorFromSet(fieldMap).String()
	items, err := c.CoreV1().Events(namespace).List(metav1.ListOptions{FieldSelector: fieldSelector})
	if err != nil {
		return
	}
	for _, item := range items.Items {
		item := parseEventDTO(&item)
		ret = append(ret, item)
	}
	return
}

func ListHpa(cluster, namespace string) (ret []dto.Hpa, err error) {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return
	}

	autoScaleList, err := c.AutoscalingV1().HorizontalPodAutoscalers(namespace).List(metav1.ListOptions{})
	if err != nil {
		return
	}

	for _, v := range autoScaleList.Items {
		ret = append(ret, *parseHpaDTO(v, cluster))
	}
	return
}

func parseHpaDTO(obj autoScaleV1.HorizontalPodAutoscaler, cluster string) *dto.Hpa {
	return &dto.Hpa{
		Name:                           obj.Name,
		Cluster:                        cluster,
		Namespace:                      obj.Namespace,
		MaxReplicas:                    obj.Spec.MaxReplicas,
		MinReplicas:                    *obj.Spec.MinReplicas,
		TargetCPUUtilizationPercentage: *obj.Spec.TargetCPUUtilizationPercentage,
	}
}

//func CreateHpa(hpa dto.HpaDTO) error {
//	c, err := client.GetKubernetesClient(hpa.Cluster)
//	if err != nil {
//		return err
//	}
//
//	_, err = c.AutoscalingV1().HorizontalPodAutoscalers(hpa.Namespace).Create(&autoScaleV1.HorizontalPodAutoscaler{
//		ObjectMeta: metav1.ObjectMeta{
//			Name:      hpa.App,
//			Namespace: hpa.Namespace,
//		},
//		Spec: autoScaleV1.HorizontalPodAutoscalerSpec{
//			MinReplicas:                    &hpa.MinReplicas,
//			MaxReplicas:                    hpa.MaxReplicas,
//			TargetCPUUtilizationPercentage: &hpa.TargetCPUUtilizationPercentage,
//			ScaleTargetRef: autoScaleV1.CrossVersionObjectReference{
//				Kind:       "Deployment",
//				Name:       hpa.App,
//				APIVersion: "apps/v1",
//			},
//		},
//	})
//	return err
//}

func DeleteHpa(cluster, namespace, app string) error {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return err
	}

	if err := c.AutoscalingV1().HorizontalPodAutoscalers(namespace).Delete(app, &metav1.DeleteOptions{}); err != nil {
		return err
	}
	return nil
}

// ClusterCapacityInfo represents the remaining allocatable resources in a cluster
type ClusterCapacityInfo struct {
	NodeCount         int   `json:"nodeCount"`
	PodCount          int   `json:"podCount"`
	CPUAllocatable    int64 `json:"cpuAllocatable"`    // milliCores
	MemoryAllocatable int64 `json:"memoryAllocatable"` // bytes
	CPUAllocated      int64 `json:"cpuAllocated"`      // milliCores
	MemoryAllocated   int64 `json:"memoryAllocated"`   // bytes
	CPUAvailable      int64 `json:"cpuAvailable"`      // milliCores (allocatable - allocated)
	MemoryAvailable   int64 `json:"memoryAvailable"`   // bytes (allocatable - allocated)
}

// GetClusterAllocatableCapacity retrieves the remaining allocatable CPU and memory capacity
// available for scheduling in a specific Kubernetes cluster
func GetClusterAllocatableCapacity(cluster string) (*ClusterCapacityInfo, error) {
	c, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return nil, fmt.Errorf("failed to get kubernetes client for cluster %s: %w", cluster, err)
	}

	// Get all nodes in the cluster
	nodeList, err := c.CoreV1().Nodes().List(metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list nodes in cluster %s: %w", cluster, err)
	}

	// Get all pods in the cluster to calculate allocated resources
	podList, err := c.CoreV1().Pods("").List(metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list pods in cluster %s: %w", cluster, err)
	}

	var totalCPUAllocatable, totalMemoryAllocatable int64
	var totalCPUAllocated, totalMemoryAllocated int64

	// Calculate total allocatable resources from all schedulable nodes
	for _, node := range nodeList.Items {
		// Skip unschedulable nodes
		if node.Spec.Unschedulable {
			continue
		}
		// Skip nodes that are not ready
		isNotReady := false
		for _, condition := range node.Status.Conditions {
			if condition.Type == corev1.NodeReady && condition.Status == corev1.ConditionFalse {
				isNotReady = true
				break
			}
		}
		if isNotReady {
			continue
		}

		// Add allocatable resources
		cpuAllocatable := node.Status.Allocatable.Cpu().MilliValue()
		memoryAllocatable := node.Status.Allocatable.Memory().Value()

		totalCPUAllocatable += cpuAllocatable
		totalMemoryAllocatable += memoryAllocatable
	}

	nodeNames := make([]string, 0, len(nodeList.Items))
	for _, node := range nodeList.Items {
		nodeNames = append(nodeNames, node.Name)
	}

	// Calculate total allocated resources from all running pods
	for _, pod := range podList.Items {
		// Skip pods that are not running or scheduled
		if pod.Status.Phase == corev1.PodSucceeded || pod.Status.Phase == corev1.PodFailed {
			continue
		}
		if !strslice.Find(nodeNames, pod.Spec.NodeName) {
			continue
		}

		// Calculate resource requests for all containers in the pod
		for _, container := range pod.Spec.Containers {
			if container.Resources.Requests != nil {
				if cpuRequest := container.Resources.Requests.Cpu(); cpuRequest != nil {
					totalCPUAllocated += cpuRequest.MilliValue()
				}
				if memoryRequest := container.Resources.Requests.Memory(); memoryRequest != nil {
					totalMemoryAllocated += memoryRequest.Value()
				}
			}
		}

		// Include init containers' resource requests as well
		//for _, initContainer := range pod.Spec.InitContainers {
		//	if initContainer.Resources.Requests != nil {
		//		if cpuRequest := initContainer.Resources.Requests.Cpu(); cpuRequest != nil {
		//			totalCPUAllocated += cpuRequest.MilliValue()
		//		}
		//		if memoryRequest := initContainer.Resources.Requests.Memory(); memoryRequest != nil {
		//			totalMemoryAllocated += memoryRequest.Value()
		//		}
		//	}
		//}
	}

	// Calculate available resources
	cpuAvailable := totalCPUAllocatable - totalCPUAllocated
	memoryAvailable := totalMemoryAllocatable - totalMemoryAllocated

	// Ensure available resources don't go negative
	if cpuAvailable < 0 {
		cpuAvailable = 0
	}
	if memoryAvailable < 0 {
		memoryAvailable = 0
	}

	return &ClusterCapacityInfo{
		NodeCount:         len(nodeList.Items),
		PodCount:          len(podList.Items),
		CPUAllocatable:    totalCPUAllocatable,
		MemoryAllocatable: totalMemoryAllocatable,
		CPUAllocated:      totalCPUAllocated,
		MemoryAllocated:   totalMemoryAllocated,
		CPUAvailable:      cpuAvailable,
		MemoryAvailable:   memoryAvailable,
	}, nil
}

func CreateEvent(cluster string, e *dto.Event) error {
	clientSet, err := k8s.GetKubernetesClient(cluster)
	if err != nil {
		return err
	}
	var involvedObject corev1.ObjectReference
	objLowerName := strings.ToLower(e.InvolvedObjectType)
	if objLowerName == "pod" {
		pod, err := clientSet.CoreV1().Pods(e.Namespace).Get(e.InvolvedObjectName, metav1.GetOptions{})
		if err != nil {
			return err
		}
		if v, err := reference.GetReference(scheme.Scheme, pod); err != nil {
			return err
		} else {
			involvedObject = *v
		}
	} else {
		return errors.New(fmt.Sprintf("[%s] type event currently not supported", e.InvolvedObjectType))
	}

	reporter := "fs-k8s-app-manager"
	hostname := reporter
	if h, err := os.Hostname(); err == nil {
		hostname = h
	}

	createTime := time.Now()

	event := &corev1.Event{
		ObjectMeta: metav1.ObjectMeta{
			Name:              fmt.Sprintf("%s-%s", e.InvolvedObjectName, time.Now().Format("20060102150405")),
			Namespace:         e.Namespace,
			CreationTimestamp: metav1.Time{Time: createTime},
			Labels: map[string]string{
				"pod":    e.InvolvedObjectName,
				"action": e.Action,
				"reason": e.Reason,
			},
		},
		InvolvedObject:      involvedObject,
		EventTime:           metav1.NowMicro(),
		ReportingController: reporter,
		ReportingInstance:   hostname,
		Action:              e.Action,
		Reason:              e.Reason,
		Related:             &involvedObject,
		Message:             e.Message,
		Type:                e.Type,
		FirstTimestamp:      metav1.Time{Time: createTime},
		LastTimestamp:       metav1.Time{Time: createTime},
		Count:               1,
		Source:              corev1.EventSource{Component: e.InvolvedObjectName, Host: hostname},
	}
	_, err = clientSet.CoreV1().Events(e.Namespace).Create(event)
	return err
}
