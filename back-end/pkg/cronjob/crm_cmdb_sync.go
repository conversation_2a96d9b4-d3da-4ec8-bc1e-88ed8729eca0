package cronjob

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/go-resty/resty/v2"
	log "github.com/sirupsen/logrus"
)

type CRMDataRecord struct {
	ID              string   `json:"_id"`
	Name            string   `json:"name"`
	DisplayName     string   `json:"display_name"`
	ServiceLevel    string   `json:"importance_level__c"`
	ServiceCategory string   `json:"service_category__c"`
	Remark          string   `json:"field_U6pmo__c"`
	MainOwner       []string `json:"owner"`
	Owners          []string `json:"field_8qntr__c"`
	EnableStatus    string   `json:"field_HwozY__c"`
	Department      []string `json:"data_own_department"`
}

type UserRecord struct {
	ID   string `json:"_id"`
	Name string `json:"name"`
}

type CRMBaseResponse struct {
	ErrCode      int         `json:"errCode"`
	ErrMessage   string      `json:"errMessage"`
	SearchSource interface{} `json:"searchSource"`
}

type CRMUserResponse struct {
	CRMBaseResponse
	Result []UserRecord `json:"result"`
}

type CRMDataResponse struct {
	CRMBaseResponse
	Result []CRMDataRecord `json:"result"`
}

func metadataClient() *resty.Client {
	//return resty.New().SetHostURL("http://************:41838").SetTimeout(120 * time.Second)
	return resty.New().SetHostURL("http://reg.foneshare.cn").SetTimeout(120 * time.Second)
}

// fetchMetadataWithPagination fetches all metadata from the API with pagination support
func fetchCRMDatas() error {
	log.Info("Starting metadata fetch job")

	const maxRecordsPerRequest = 500
	const apiEndpoint = "/fs-metadata-rest/paas/metadata/data/find/by/template/projection"

	var allRecords []CRMDataRecord
	offset := 0
	totalFetched := 0
	maxRequests := 100

	client := metadataClient()

	for {
		templateJSON := map[string]string{
			"limit":  strconv.Itoa(maxRecordsPerRequest),
			"offset": strconv.Itoa(offset),
		}

		templateJSONBytes, err := json.Marshal(templateJSON)
		if err != nil {
			log.Errorf("Failed to marshal template JSON: %v", err)
			return fmt.Errorf("failed to marshal template JSON: %w", err)
		}

		request := map[string]interface{}{
			"fieldList": []string{
				"_id",
				"name",
				"display_name",
				"importance_level__c",
				"service_category__c",
				"field_U6pmo__c",
				"owner",
				"field_8qntr__c",
				"field_HwozY__c",
				"data_own_department",
			},
			"tenantId":              "1",
			"userId":                "-10000",
			"objectDescribeAPIName": "object_5FxRC__c",
			"templateJson":          string(templateJSONBytes),
		}

		log.Infof("Fetching metadata batch: offset=%d, limit=%d", offset, maxRecordsPerRequest)

		// Make API request
		resp, err := client.R().
			SetHeader("Content-Type", "application/json").
			SetBody(request).
			Post(apiEndpoint)
		maxRequests--
		if maxRequests <= 0 {
			log.Warn("Max requests reached, stopping")
			break
		}

		if err != nil {
			log.Errorf("Failed to make API request: %v", err)
			return fmt.Errorf("failed to make API request: %w", err)
		}

		if resp.StatusCode() != 200 {
			log.Errorf("API request failed with status %d: %s", resp.StatusCode(), string(resp.Body()))
			return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode(), string(resp.Body()))
		}

		// Parse response
		var metadataResp CRMDataResponse
		if err := json.Unmarshal(resp.Body(), &metadataResp); err != nil {
			log.Errorf("Failed to unmarshal response: %v", err)
			return fmt.Errorf("failed to unmarshal response: %w", err)
		}

		// Check for API errors
		if metadataResp.ErrCode != 0 {
			log.Errorf("API returned error: code=%d, message=%s", metadataResp.ErrCode, metadataResp.ErrMessage)
			return fmt.Errorf("API error: code=%d, message=%s", metadataResp.ErrCode, metadataResp.ErrMessage)
		}

		// Add records to collection
		batchSize := len(metadataResp.Result)
		allRecords = append(allRecords, metadataResp.Result...)
		totalFetched += batchSize

		log.Infof("Fetched %d records in this batch, total so far: %d", batchSize, totalFetched)

		// Check if we've reached the end (no more records)
		if batchSize < maxRecordsPerRequest {
			log.Infof("Reached end of data. Fetched %d records in this batch (less than limit of %d)", batchSize, maxRecordsPerRequest)
			break
		}

		// Update offset for next iteration
		offset += maxRecordsPerRequest

		// Add a small delay to avoid overwhelming the API
		time.Sleep(100 * time.Millisecond)
	}

	log.Infof("Metadata fetch completed successfully. Total records fetched: %d", totalFetched)

	return nil
}

func fetchUsersFromCRM() error {

	const maxRecordsPerRequest = 500
	const apiEndpoint = "/fs-metadata-rest/paas/metadata/data/find/by/template/projection"

	var allRecords []UserRecord
	offset := 0
	totalFetched := 0
	maxRequests := 100

	client := metadataClient()

	for {
		templateJSON := map[string]string{
			"limit":  strconv.Itoa(maxRecordsPerRequest),
			"offset": strconv.Itoa(offset),
		}

		templateJSONBytes, err := json.Marshal(templateJSON)
		if err != nil {
			log.Errorf("Failed to marshal template JSON: %v", err)
			return fmt.Errorf("failed to marshal template JSON: %w", err)
		}

		request := map[string]interface{}{
			"fieldList": []string{
				"user_id",
				"name",
			},
			"tenantId":              "1",
			"userId":                "-10000",
			"objectDescribeAPIName": "PersonnelObj",
			"templateJson":          string(templateJSONBytes),
		}

		log.Infof("Fetching metadata batch: offset=%d, limit=%d", offset, maxRecordsPerRequest)

		// Make API request
		resp, err := client.R().
			SetHeader("Content-Type", "application/json").
			SetBody(request).
			Post(apiEndpoint)
		maxRequests--
		if maxRequests <= 0 {
			log.Warn("Max requests reached, stopping")
			break
		}

		if err != nil {
			log.Errorf("Failed to make API request: %v", err)
			return fmt.Errorf("failed to make API request: %w", err)
		}

		if resp.StatusCode() != 200 {
			log.Errorf("API request failed with status %d: %s", resp.StatusCode(), string(resp.Body()))
			return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode(), string(resp.Body()))
		}

		// Parse response
		var records CRMUserResponse
		if err := json.Unmarshal(resp.Body(), &records); err != nil {
			log.Errorf("Failed to unmarshal response: %v", err)
			return fmt.Errorf("failed to unmarshal response: %w", err)
		}

		// Check for API errors
		if records.ErrCode != 0 {
			log.Errorf("API returned error: code=%d, message=%s", records.ErrCode, records.ErrMessage)
			return fmt.Errorf("API error: code=%d, message=%s", records.ErrCode, records.ErrMessage)
		}

		// Add records to collection
		batchSize := len(records.Result)
		allRecords = append(allRecords, records.Result...)
		totalFetched += batchSize

		log.Infof("Fetched %d records in this batch, total so far: %d", batchSize, totalFetched)

		// Check if we've reached the end (no more records)
		if batchSize < maxRecordsPerRequest {
			log.Infof("Reached end of data. Fetched %d records in this batch (less than limit of %d)", batchSize, maxRecordsPerRequest)
			break
		}

		// Update offset for next iteration
		offset += maxRecordsPerRequest

		// Add a small delay to avoid overwhelming the API
		time.Sleep(100 * time.Millisecond)
	}

	log.Infof("Metadata fetch completed successfully. Total records fetched: %d", totalFetched)

	return nil
}
