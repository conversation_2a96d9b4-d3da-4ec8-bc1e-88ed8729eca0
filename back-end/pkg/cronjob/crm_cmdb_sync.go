package cronjob

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/go-resty/resty/v2"
	log "github.com/sirupsen/logrus"
)

type CRMDataRecord struct {
	ID              string   `json:"_id"`
	Name            string   `json:"name"`
	DisplayName     string   `json:"display_name"`
	ServiceLevel    string   `json:"importance_level__c"`
	ServiceCategory string   `json:"service_category__c"`
	Remark          string   `json:"field_U6pmo__c"`
	MainOwner       []string `json:"owner"`
	Owners          []string `json:"field_8qntr__c"`
	EnableStatus    string   `json:"field_HwozY__c"`
	Department      []string `json:"data_own_department"`
}

type UserRecord struct {
	UserId string `json:"user_id"`
	Name   string `json:"name"`
}

type DeptRecord struct {
	DeptId string `json:"dept_id"`
	Name   string `json:"name"`
}

type CRMBaseResponse struct {
	ErrCode      int         `json:"errCode"`
	ErrMessage   string      `json:"errMessage"`
	SearchSource interface{} `json:"searchSource"`
}

type CRMUserResponse struct {
	CRMBaseResponse
	Result []UserRecord `json:"result"`
}

type CRMDeptResponse struct {
	CRMBaseResponse
	Result []DeptRecord `json:"result"`
}

type CRMDeptResponse struct {
	CRMBaseResponse
	Result []DeptRecord `json:"result"`
}

type CRMDataResponse struct {
	CRMBaseResponse
	Result []CRMDataRecord `json:"result"`
}

// Generic interfaces and types for abstraction

// CRMRecord represents any record type that can be fetched from CRM
type CRMRecord interface {
	CRMDataRecord | UserRecord | DeptRecord
}

// CRMResponse represents any response type from CRM API
type CRMResponse[T CRMRecord] interface {
	GetErrCode() int
	GetErrMessage() string
	GetResult() []T
}

// Implement CRMResponse interface for existing response types
func (r CRMDataResponse) GetErrCode() int     { return r.ErrCode }
func (r CRMDataResponse) GetErrMessage() string { return r.ErrMessage }
func (r CRMDataResponse) GetResult() []CRMDataRecord { return r.Result }

func (r CRMUserResponse) GetErrCode() int     { return r.ErrCode }
func (r CRMUserResponse) GetErrMessage() string { return r.ErrMessage }
func (r CRMUserResponse) GetResult() []UserRecord { return r.Result }

// FetchConfig holds configuration for different types of CRM data fetching
type FetchConfig struct {
	ObjectAPIName string
	FieldList     []string
	LogName       string // For logging purposes
}

// Predefined configurations for different record types
var (
	CRMDataConfig = FetchConfig{
		ObjectAPIName: "object_5FxRC__c",
		FieldList: []string{
			"_id",
			"name",
			"display_name",
			"importance_level__c",
			"service_category__c",
			"field_U6pmo__c",
			"owner",
			"field_8qntr__c",
			"field_HwozY__c",
			"data_own_department",
		},
		LogName: "CRM Data",
	}

	UserConfig = FetchConfig{
		ObjectAPIName: "PersonnelObj",
		FieldList: []string{
			"user_id",
			"name",
		},
		LogName: "Users",
	}

	DeptConfig = FetchConfig{
		ObjectAPIName: "DepartmentObj",
		FieldList: []string{
			"user_id",
			"name",
		},
		LogName: "Departments",
	}
)

func metadataClient() *resty.Client {
	//return resty.New().SetHostURL("http://************:41838").SetTimeout(120 * time.Second)
	return resty.New().SetHostURL("http://reg.foneshare.cn").SetTimeout(120 * time.Second)
}

// Generic pagination function that works with any CRM record type
func fetchWithPagination[T CRMRecord](config FetchConfig, unmarshalFunc func([]byte) ([]T, int, string, error)) ([]T, error) {
	log.Infof("Starting %s fetch job", config.LogName)

	const maxRecordsPerRequest = 500
	const apiEndpoint = "/fs-metadata-rest/paas/metadata/data/find/by/template/projection"

	var allRecords []T
	offset := 0
	totalFetched := 0
	maxRequests := 100

	client := metadataClient()

	for {
		templateJSON := map[string]string{
			"limit":  strconv.Itoa(maxRecordsPerRequest),
			"offset": strconv.Itoa(offset),
		}

		templateJSONBytes, err := json.Marshal(templateJSON)
		if err != nil {
			log.Errorf("Failed to marshal template JSON: %v", err)
			return nil, fmt.Errorf("failed to marshal template JSON: %w", err)
		}

		request := map[string]interface{}{
			"fieldList":             config.FieldList,
			"tenantId":              "1",
			"userId":                "-10000",
			"objectDescribeAPIName": config.ObjectAPIName,
			"templateJson":          string(templateJSONBytes),
		}

		log.Infof("Fetching %s batch: offset=%d, limit=%d", config.LogName, offset, maxRecordsPerRequest)

		// Make API request
		resp, err := client.R().
			SetHeader("Content-Type", "application/json").
			SetBody(request).
			Post(apiEndpoint)
		maxRequests--
		if maxRequests <= 0 {
			log.Warn("Max requests reached, stopping")
			break
		}

		if err != nil {
			log.Errorf("Failed to make API request: %v", err)
			return nil, fmt.Errorf("failed to make API request: %w", err)
		}

		if resp.StatusCode() != 200 {
			log.Errorf("API request failed with status %d: %s", resp.StatusCode(), string(resp.Body()))
			return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode(), string(resp.Body()))
		}

		// Parse response using the provided unmarshal function
		records, errCode, errMessage, err := unmarshalFunc(resp.Body())
		if err != nil {
			log.Errorf("Failed to unmarshal response: %v", err)
			return nil, fmt.Errorf("failed to unmarshal response: %w", err)
		}

		// Check for API errors
		if errCode != 0 {
			log.Errorf("API returned error: code=%d, message=%s", errCode, errMessage)
			return nil, fmt.Errorf("API error: code=%d, message=%s", errCode, errMessage)
		}

		// Add records to collection
		batchSize := len(records)
		allRecords = append(allRecords, records...)
		totalFetched += batchSize

		log.Infof("Fetched %d records in this batch, total so far: %d", batchSize, totalFetched)

		// Check if we've reached the end (no more records)
		if batchSize < maxRecordsPerRequest {
			log.Infof("Reached end of data. Fetched %d records in this batch (less than limit of %d)", batchSize, maxRecordsPerRequest)
			break
		}

		// Update offset for next iteration
		offset += maxRecordsPerRequest

		// Add a small delay to avoid overwhelming the API
		time.Sleep(100 * time.Millisecond)
	}

	log.Infof("%s fetch completed successfully. Total records fetched: %d", config.LogName, totalFetched)
	return allRecords, nil
}

// Unmarshal helper functions for different response types
func unmarshalCRMDataResponse(data []byte) ([]CRMDataRecord, int, string, error) {
	var response CRMDataResponse
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, 0, "", err
	}
	return response.Result, response.ErrCode, response.ErrMessage, nil
}

func unmarshalUserResponse(data []byte) ([]UserRecord, int, string, error) {
	var response CRMUserResponse
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, 0, "", err
	}
	return response.Result, response.ErrCode, response.ErrMessage, nil
}

func unmarshalDeptResponse(data []byte) ([]UserRecord, int, string, error) {
	var response CRMUserResponse // Note: DeptRecord uses same structure as UserRecord
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, 0, "", err
	}
	return response.Result, response.ErrCode, response.ErrMessage, nil
}

// fetchCRMDatas fetches all CRM data records using the generic pagination handler
func fetchCRMDatas() error {
	_, err := fetchWithPagination(CRMDataConfig, unmarshalCRMDataResponse)
	return err
}

// fetchUsersFromCRM fetches all user records using the generic pagination handler
func fetchUsersFromCRM() error {
	_, err := fetchWithPagination(UserConfig, unmarshalUserResponse)
	return err
}

func fetchDeptFromCRM() error {

	const maxRecordsPerRequest = 500
	const apiEndpoint = "/fs-metadata-rest/paas/metadata/data/find/by/template/projection"

	var allRecords []DeptRecord
	offset := 0
	totalFetched := 0
	maxRequests := 100

	client := metadataClient()

	for {
		templateJSON := map[string]string{
			"limit":  strconv.Itoa(maxRecordsPerRequest),
			"offset": strconv.Itoa(offset),
		}

		templateJSONBytes, err := json.Marshal(templateJSON)
		if err != nil {
			log.Errorf("Failed to marshal template JSON: %v", err)
			return fmt.Errorf("failed to marshal template JSON: %w", err)
		}

		request := map[string]interface{}{
			"fieldList": []string{
				"user_id",
				"name",
			},
			"tenantId":              "1",
			"userId":                "-10000",
			"objectDescribeAPIName": "DepartmentObj",
			"templateJson":          string(templateJSONBytes),
		}

		log.Infof("Fetching metadata batch: offset=%d, limit=%d", offset, maxRecordsPerRequest)

		// Make API request
		resp, err := client.R().
			SetHeader("Content-Type", "application/json").
			SetBody(request).
			Post(apiEndpoint)
		maxRequests--
		if maxRequests <= 0 {
			log.Warn("Max requests reached, stopping")
			break
		}

		if err != nil {
			log.Errorf("Failed to make API request: %v", err)
			return fmt.Errorf("failed to make API request: %w", err)
		}

		if resp.StatusCode() != 200 {
			log.Errorf("API request failed with status %d: %s", resp.StatusCode(), string(resp.Body()))
			return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode(), string(resp.Body()))
		}

		// Parse response
		var records CRMDeptResponse
		if err := json.Unmarshal(resp.Body(), &records); err != nil {
			log.Errorf("Failed to unmarshal response: %v", err)
			return fmt.Errorf("failed to unmarshal response: %w", err)
		}

		// Check for API errors
		if records.ErrCode != 0 {
			log.Errorf("API returned error: code=%d, message=%s", records.ErrCode, records.ErrMessage)
			return fmt.Errorf("API error: code=%d, message=%s", records.ErrCode, records.ErrMessage)
		}

		// Add records to collection
		batchSize := len(records.Result)
		allRecords = append(allRecords, records.Result...)
		totalFetched += batchSize

		log.Infof("Fetched %d records in this batch, total so far: %d", batchSize, totalFetched)

		// Check if we've reached the end (no more records)
		if batchSize < maxRecordsPerRequest {
			log.Infof("Reached end of data. Fetched %d records in this batch (less than limit of %d)", batchSize, maxRecordsPerRequest)
			break
		}

		// Update offset for next iteration
		offset += maxRecordsPerRequest

		// Add a small delay to avoid overwhelming the API
		time.Sleep(100 * time.Millisecond)
	}

	log.Infof("Metadata fetch completed successfully. Total records fetched: %d", totalFetched)

	return nil
}
