package cronjob

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/go-resty/resty/v2"
	log "github.com/sirupsen/logrus"
)

// Test data structures
var testCRMDataRecords = []CRMDataRecord{
	{
		ID:              "test-id-1",
		Name:            "test-app-1",
		DisplayName:     "Test Application 1",
		ServiceLevel:    "high",
		ServiceCategory: "web",
		Remark:          "test remark",
		MainOwner:       []string{"owner1"},
		Owners:          []string{"owner1", "owner2"},
		EnableStatus:    "enabled",
		Department:      []string{"dept1"},
	},
	{
		ID:              "test-id-2",
		Name:            "test-app-2",
		DisplayName:     "Test Application 2",
		ServiceLevel:    "medium",
		ServiceCategory: "api",
		Remark:          "test remark 2",
		MainOwner:       []string{"owner2"},
		Owners:          []string{"owner2", "owner3"},
		EnableStatus:    "enabled",
		Department:      []string{"dept2"},
	},
}

var testUserRecords = []UserRecord{
	{UserId: "user1", Name: "Test User 1"},
	{UserId: "user2", Name: "Test User 2"},
}

var testDeptRecords = []DeptRecord{
	{UserId: "dept1", Name: "Test Department 1"},
	{UserId: "dept2", Name: "Test Department 2"},
}

// Mock server helper function
func createMockServer(responses []interface{}, errCode int, errMessage string) *httptest.Server {
	responseIndex := 0
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/fs-metadata-rest/paas/metadata/data/find/by/template/projection" {
			http.Error(w, "Not found", http.StatusNotFound)
			return
		}

		w.Header().Set("Content-Type", "application/json")

		// Return different responses for pagination
		var response interface{}
		if responseIndex < len(responses) {
			response = responses[responseIndex]
			responseIndex++
		} else {
			// Return empty response to end pagination
			switch responses[0].(type) {
			case CRMDataResponse:
				response = CRMDataResponse{
					CRMBaseResponse: CRMBaseResponse{ErrCode: errCode, ErrMessage: errMessage},
					Result:          []CRMDataRecord{},
				}
			case CRMUserResponse:
				response = CRMUserResponse{
					CRMBaseResponse: CRMBaseResponse{ErrCode: errCode, ErrMessage: errMessage},
					Result:          []UserRecord{},
				}
			case CRMDeptResponse:
				response = CRMDeptResponse{
					CRMBaseResponse: CRMBaseResponse{ErrCode: errCode, ErrMessage: errMessage},
					Result:          []DeptRecord{},
				}
			}
		}

		json.NewEncoder(w).Encode(response)
	}))
}

// Test the generic fetchWithPagination function with CRM data
func TestFetchWithPagination_CRMData(t *testing.T) {
	// Create mock responses
	responses := []interface{}{
		CRMDataResponse{
			CRMBaseResponse: CRMBaseResponse{ErrCode: 0, ErrMessage: ""},
			Result:          testCRMDataRecords,
		},
	}

	server := createMockServer(responses, 0, "")
	defer server.Close()

	// Override the metadataClient function for testing
	originalClient := metadataClient
	defer func() { metadataClient = originalClient }()
	metadataClient = func() *resty.Client {
		return resty.New().SetHostURL(server.URL).SetTimeout(10 * time.Second)
	}

	// Test the function
	records, err := fetchWithPagination(CRMDataConfig, unmarshalCRMDataResponse)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}

	if len(records) != len(testCRMDataRecords) {
		t.Fatalf("Expected %d records, got %d", len(testCRMDataRecords), len(records))
	}

	// Verify record content
	for i, record := range records {
		if record.ID != testCRMDataRecords[i].ID {
			t.Errorf("Expected ID %s, got %s", testCRMDataRecords[i].ID, record.ID)
		}
		if record.Name != testCRMDataRecords[i].Name {
			t.Errorf("Expected Name %s, got %s", testCRMDataRecords[i].Name, record.Name)
		}
	}
}

// Test the generic fetchWithPagination function with User data
func TestFetchWithPagination_Users(t *testing.T) {
	responses := []interface{}{
		CRMUserResponse{
			CRMBaseResponse: CRMBaseResponse{ErrCode: 0, ErrMessage: ""},
			Result:          testUserRecords,
		},
	}

	server := createMockServer(responses, 0, "")
	defer server.Close()

	originalClient := metadataClient
	defer func() { metadataClient = originalClient }()
	metadataClient = func() *resty.Client {
		return resty.New().SetHostURL(server.URL).SetTimeout(10 * time.Second)
	}

	records, err := fetchWithPagination(UserConfig, unmarshalUserResponse)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}

	if len(records) != len(testUserRecords) {
		t.Fatalf("Expected %d records, got %d", len(testUserRecords), len(records))
	}

	for i, record := range records {
		if record.UserId != testUserRecords[i].UserId {
			t.Errorf("Expected UserId %s, got %s", testUserRecords[i].UserId, record.UserId)
		}
	}
}

// Test the generic fetchWithPagination function with Department data
func TestFetchWithPagination_Departments(t *testing.T) {
	responses := []interface{}{
		CRMDeptResponse{
			CRMBaseResponse: CRMBaseResponse{ErrCode: 0, ErrMessage: ""},
			Result:          testDeptRecords,
		},
	}

	server := createMockServer(responses, 0, "")
	defer server.Close()

	originalClient := metadataClient
	defer func() { metadataClient = originalClient }()
	metadataClient = func() *resty.Client {
		return resty.New().SetHostURL(server.URL).SetTimeout(10 * time.Second)
	}

	records, err := fetchWithPagination(DeptConfig, unmarshalDeptResponse)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}

	if len(records) != len(testDeptRecords) {
		t.Fatalf("Expected %d records, got %d", len(testDeptRecords), len(records))
	}

	for i, record := range records {
		if record.UserId != testDeptRecords[i].UserId {
			t.Errorf("Expected UserId %s, got %s", testDeptRecords[i].UserId, record.UserId)
		}
	}
}

// Test error handling
func TestFetchWithPagination_APIError(t *testing.T) {
	responses := []interface{}{
		CRMDataResponse{
			CRMBaseResponse: CRMBaseResponse{ErrCode: 500, ErrMessage: "Internal Server Error"},
			Result:          []CRMDataRecord{},
		},
	}

	server := createMockServer(responses, 500, "Internal Server Error")
	defer server.Close()

	originalClient := metadataClient
	defer func() { metadataClient = originalClient }()
	metadataClient = func() *resty.Client {
		return resty.New().SetHostURL(server.URL).SetTimeout(10 * time.Second)
	}

	_, err := fetchWithPagination(CRMDataConfig, unmarshalCRMDataResponse)
	if err == nil {
		t.Fatal("Expected error, got nil")
	}

	expectedError := "API error: code=500, message=Internal Server Error"
	if err.Error() != expectedError {
		t.Errorf("Expected error '%s', got '%s'", expectedError, err.Error())
	}
}

// Test HTTP error handling
func TestFetchWithPagination_HTTPError(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		http.Error(w, "Server Error", http.StatusInternalServerError)
	}))
	defer server.Close()

	originalClient := metadataClient
	defer func() { metadataClient = originalClient }()
	metadataClient = func() *resty.Client {
		return resty.New().SetHostURL(server.URL).SetTimeout(10 * time.Second)
	}

	_, err := fetchWithPagination(CRMDataConfig, unmarshalCRMDataResponse)
	if err == nil {
		t.Fatal("Expected error, got nil")
	}
}

// Test pagination with multiple pages
func TestFetchWithPagination_MultiplePagesSuccess(t *testing.T) {
	// Disable logging for cleaner test output
	log.SetLevel(log.FatalLevel)
	defer log.SetLevel(log.InfoLevel)

	// Create responses for multiple pages
	page1Records := testCRMDataRecords[:1]
	page2Records := testCRMDataRecords[1:]

	responses := []interface{}{
		CRMDataResponse{
			CRMBaseResponse: CRMBaseResponse{ErrCode: 0, ErrMessage: ""},
			Result:          page1Records,
		},
		CRMDataResponse{
			CRMBaseResponse: CRMBaseResponse{ErrCode: 0, ErrMessage: ""},
			Result:          page2Records,
		},
	}

	server := createMockServer(responses, 0, "")
	defer server.Close()

	originalClient := metadataClient
	defer func() { metadataClient = originalClient }()
	metadataClient = func() *resty.Client {
		return resty.New().SetHostURL(server.URL).SetTimeout(10 * time.Second)
	}

	records, err := fetchWithPagination(CRMDataConfig, unmarshalCRMDataResponse)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}

	expectedTotal := len(page1Records) + len(page2Records)
	if len(records) != expectedTotal {
		t.Fatalf("Expected %d total records, got %d", expectedTotal, len(records))
	}
}

// Test the refactored public functions
func TestFetchCRMDatas(t *testing.T) {
	responses := []interface{}{
		CRMDataResponse{
			CRMBaseResponse: CRMBaseResponse{ErrCode: 0, ErrMessage: ""},
			Result:          testCRMDataRecords,
		},
	}

	server := createMockServer(responses, 0, "")
	defer server.Close()

	originalClient := metadataClient
	defer func() { metadataClient = originalClient }()
	metadataClient = func() *resty.Client {
		return resty.New().SetHostURL(server.URL).SetTimeout(10 * time.Second)
	}

	err := fetchCRMDatas()
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
}

func TestFetchUsersFromCRM(t *testing.T) {
	responses := []interface{}{
		CRMUserResponse{
			CRMBaseResponse: CRMBaseResponse{ErrCode: 0, ErrMessage: ""},
			Result:          testUserRecords,
		},
	}

	server := createMockServer(responses, 0, "")
	defer server.Close()

	originalClient := metadataClient
	defer func() { metadataClient = originalClient }()
	metadataClient = func() *resty.Client {
		return resty.New().SetHostURL(server.URL).SetTimeout(10 * time.Second)
	}

	err := fetchUsersFromCRM()
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
}

func TestFetchDeptFromCRM(t *testing.T) {
	responses := []interface{}{
		CRMDeptResponse{
			CRMBaseResponse: CRMBaseResponse{ErrCode: 0, ErrMessage: ""},
			Result:          testDeptRecords,
		},
	}

	server := createMockServer(responses, 0, "")
	defer server.Close()

	originalClient := metadataClient
	defer func() { metadataClient = originalClient }()
	metadataClient = func() *resty.Client {
		return resty.New().SetHostURL(server.URL).SetTimeout(10 * time.Second)
	}

	err := fetchDeptFromCRM()
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
}

// Test unmarshal functions
func TestUnmarshalFunctions(t *testing.T) {
	// Test CRM Data unmarshal
	crmDataResp := CRMDataResponse{
		CRMBaseResponse: CRMBaseResponse{ErrCode: 0, ErrMessage: ""},
		Result:          testCRMDataRecords,
	}
	data, _ := json.Marshal(crmDataResp)
	records, errCode, errMsg, err := unmarshalCRMDataResponse(data)
	if err != nil {
		t.Fatalf("unmarshalCRMDataResponse failed: %v", err)
	}
	if errCode != 0 || errMsg != "" {
		t.Errorf("Expected errCode=0 and empty errMsg, got errCode=%d, errMsg=%s", errCode, errMsg)
	}
	if len(records) != len(testCRMDataRecords) {
		t.Errorf("Expected %d records, got %d", len(testCRMDataRecords), len(records))
	}

	// Test User unmarshal
	userResp := CRMUserResponse{
		CRMBaseResponse: CRMBaseResponse{ErrCode: 0, ErrMessage: ""},
		Result:          testUserRecords,
	}
	data, _ = json.Marshal(userResp)
	userRecords, errCode, errMsg, err := unmarshalUserResponse(data)
	if err != nil {
		t.Fatalf("unmarshalUserResponse failed: %v", err)
	}
	if errCode != 0 || errMsg != "" {
		t.Errorf("Expected errCode=0 and empty errMsg, got errCode=%d, errMsg=%s", errCode, errMsg)
	}
	if len(userRecords) != len(testUserRecords) {
		t.Errorf("Expected %d records, got %d", len(testUserRecords), len(userRecords))
	}

	// Test Dept unmarshal
	deptResp := CRMDeptResponse{
		CRMBaseResponse: CRMBaseResponse{ErrCode: 0, ErrMessage: ""},
		Result:          testDeptRecords,
	}
	data, _ = json.Marshal(deptResp)
	deptRecords, errCode, errMsg, err := unmarshalDeptResponse(data)
	if err != nil {
		t.Fatalf("unmarshalDeptResponse failed: %v", err)
	}
	if errCode != 0 || errMsg != "" {
		t.Errorf("Expected errCode=0 and empty errMsg, got errCode=%d, errMsg=%s", errCode, errMsg)
	}
	if len(deptRecords) != len(testDeptRecords) {
		t.Errorf("Expected %d records, got %d", len(testDeptRecords), len(deptRecords))
	}
}

// Test configuration validation
func TestFetchConfigs(t *testing.T) {
	// Test CRM Data config
	if CRMDataConfig.ObjectAPIName != "object_5FxRC__c" {
		t.Errorf("Expected ObjectAPIName 'object_5FxRC__c', got '%s'", CRMDataConfig.ObjectAPIName)
	}
	if len(CRMDataConfig.FieldList) != 10 {
		t.Errorf("Expected 10 fields in CRMDataConfig, got %d", len(CRMDataConfig.FieldList))
	}

	// Test User config
	if UserConfig.ObjectAPIName != "PersonnelObj" {
		t.Errorf("Expected ObjectAPIName 'PersonnelObj', got '%s'", UserConfig.ObjectAPIName)
	}
	if len(UserConfig.FieldList) != 2 {
		t.Errorf("Expected 2 fields in UserConfig, got %d", len(UserConfig.FieldList))
	}

	// Test Dept config
	if DeptConfig.ObjectAPIName != "DepartmentObj" {
		t.Errorf("Expected ObjectAPIName 'DepartmentObj', got '%s'", DeptConfig.ObjectAPIName)
	}
	if len(DeptConfig.FieldList) != 2 {
		t.Errorf("Expected 2 fields in DeptConfig, got %d", len(DeptConfig.FieldList))
	}
}
