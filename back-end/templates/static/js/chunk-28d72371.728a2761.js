(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-28d72371"],{"0736":function(t,e,n){},"11e9":function(t,e,n){var o=n("52a7"),a=n("4630"),s=n("6821"),i=n("6a99"),r=n("69a8"),c=n("c69a"),l=Object.getOwnPropertyDescriptor;e.f=n("9e1e")?l:function(t,e){if(t=s(t),e=i(e,!0),c)try{return l(t,e)}catch(n){}if(r(t,e))return a(!o.f.call(t,e),t[e])}},1262:function(t,e,n){"use strict";var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.eventsLoading,expression:"eventsLoading"}],staticClass:"pod-event",attrs:{"element-loading-text":"事件加载中..."}},[t.events&&t.events.length>0?n("div",[n("el-timeline",t._l(t.events,(function(e,o){return n("el-timeline-item",{key:o,attrs:{placement:"top","hide-timestamp":!0,timestamp:e.lastTime?e.lastTime:e.createTime}},[n("div",[n("span",[t._v(t._s(e.lastTime?e.lastTime:e.createTime))]),t._v(" "),n("el-tag",{staticStyle:{margin:"0 10px"},attrs:{size:"small",type:"Warning"===e.type?"warning":"info"}},[t._v(t._s(e.type))]),t._v(" "),n("span",{staticStyle:{padding:"0 10px"}},[t._v(" ( x"+t._s(e.count)+" )")]),t._v(" "),n("span",{staticStyle:{"padding-right":"10px"}},[t._v(t._s(e.reason)+":")]),t._v(" "+t._s(e.message)+"\n        ")],1)])})),1)],1):t.events&&0===t.events.length?n("div",[n("el-empty",{attrs:{description:"暂无事件"}})],1):t._e()])},a=[],s=(n("6762"),n("2fdb"),n("a527")),i={props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},pod:{type:String,required:!0},hiddenStartUpEvent:{type:Boolean,default:!0}},created:function(){var t=this;this.$parent.$el&&this.$parent.$el.offsetParent&&this.$parent.$el.offsetParent.className&&this.$parent.$el.offsetParent.className.includes("fixed")||this.$nextTick((function(){t.loadEvents()}))},computed:{},data:function(){return{events:null,eventsLoading:!1}},methods:{loadEvents:function(){var t=this;this.eventsLoading=!0,Object(s["n"])(this.cluster,this.namespace,this.pod).then((function(e){var n=e.data;t.hiddenStartUpEvent&&(n=n.filter((function(t){return!t.message.includes("Startup probe failed")}))),t.events=n})).catch((function(e){t.$message.error("pod事件加载失败: "+e.message)})).finally((function(){t.eventsLoading=!1}))}}},r=i,c=n("2877"),l=Object(c["a"])(r,o,a,!1,null,null,null);e["a"]=l.exports},"2fdb":function(t,e,n){"use strict";var o=n("5ca1"),a=n("d2c8"),s="includes";o(o.P+o.F*n("5147")(s),"String",{includes:function(t){return!!~a(this,t,s).indexOf(t,arguments.length>1?arguments[1]:void 0)}})},3336:function(t,e,n){},4331:function(t,e,n){"use strict";n.r(e);var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container app-deploy-detail",staticStyle:{"min-height":"1200px"}},[n("div",{staticStyle:{width:"960px",margin:"0 auto"}},[n("el-steps",{attrs:{active:2,"align-center":""}},t._l(t.tasks,(function(e,o){return n("el-step",{staticStyle:{cursor:"pointer"},attrs:{title:e.title,description:"",status:e.status,icon:e.icon},nativeOn:{click:function(e){return t.stepClick(o)}}},[n("template",{slot:"description"},[t._v("\n                "+t._s(e.statusDesc)+"\n                "),t.job.beforeJobId>0?n("div",{staticStyle:{display:"inline-block"}},[n("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"前置任务成功，开始执行当前任务；前置任务失败，当前任务取消",placement:"top"}},[n("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"},on:{click:function(e){return t.jobDetailPage(t.job.beforeJobId)}}},[t._v("\n                      (查看前置任务)\n                    ")])],1)],1):t._e()])],2)})),1)],1),t._v(" "),n("div",{staticStyle:{position:"relative",height:"20px"}},[n("div",{staticStyle:{float:"right","padding-bottom":"5px"}},[["RUNNING","WAIT"].includes(this.job.status)?n("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-switch-button"},on:{click:function(e){return t.cancel()}}},[t._v("取消发布\n      ")]):n("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-refresh-left"},on:{click:function(e){return t.redo()}}},[t._v("重发\n      ")]),t._v(" "),n("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-position"},on:{click:function(e){return t.pipelinePage()}}},[t._v("发布流程页\n      ")]),t._v(" "),n("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-menu"},on:{click:function(e){return t.podPage()}}},[t._v("实例管理页\n      ")])],1),t._v(" "),n("div",{staticStyle:{"font-size":"14px","font-weight":"bold",color:"rgb(64, 158, 255)",float:"left"}},[t._v("信息\n      "),n("div",{staticStyle:{"margin-left":"60px",display:"inline-block"}},[n("el-button",{staticStyle:{padding:"0",color:"#ccc","font-size":"10px"},attrs:{type:"text"},on:{click:function(e){return t.setJobFailed()}}},[t._v("设置为失败\n        ")]),t._v(" "),n("el-button",{staticStyle:{padding:"0",color:"#ccc","font-size":"10px"},attrs:{type:"text"},on:{click:function(e){return t.setJobSuccess()}}},[t._v("设置为成功\n        ")])],1)])]),t._v(" "),n("el-card",{directives:[{name:"loading",rawName:"v-loading",value:t.jobLoading,expression:"jobLoading"}],staticClass:"box-card",staticStyle:{clear:"both"}},[n("el-descriptions",{attrs:{column:4,border:""}},[n("el-descriptions-item",{attrs:{label:"应用"}},[t._v(t._s(this.job.params.app))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"集群"}},[t._v(t._s(this.job.params.cluster))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"环境"}},[t._v(t._s(this.job.params.namespace))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"发布批次"}},[t._v(t._s(this.job.params.maxSurge))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"发布版本"}},[t._v(t._s(this.job.params.tag))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"备注"}},[t._v(t._s(this.job.remark))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"操作人"}},[t._v(t._s(this.job.author))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"操作时间"}},[t._v(t._s(this.job.createdAt))]),t._v(" "),n("el-descriptions-item",{attrs:{label:"",span:"4"}},[n("template",{slot:"label"},[t._v("\n          部署模块\n        ")]),t._v(" "),n("div",{staticClass:"deploy-module-table",staticStyle:{"margin-top":"-10px","margin-left":"-10px"}},[n("el-table",{staticStyle:{width:"100%"},attrs:{size:"mini",data:t.job.params.deployModules}},[n("el-table-column",{attrs:{type:"index",width:"50"}}),t._v(" "),n("el-table-column",{attrs:{prop:"gitUrl",label:"Git地址"}}),t._v(" "),n("el-table-column",{attrs:{prop:"module",label:"子模块"}}),t._v(" "),n("el-table-column",{attrs:{prop:"contextPath",label:"ContextPath"}}),t._v(" "),n("el-table-column",{attrs:{prop:"tag",label:"Git 分支|标签"}}),t._v(" "),n("el-table-column",{attrs:{label:"镜像版本"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                "+t._s(e.row.artifactImage.split(":").pop())+"\n              ")]}}])})],1)],1)],2)],1)],1),t._v(" "),n("div",{staticStyle:{"margin-top":"20px","padding-bottom":"3px"}},[n("span",{staticStyle:{"font-size":"14px","font-weight":"bold",color:"rgb(64, 158, 255)"}},[t._v("\n      实例（pod）列表\n    ")]),t._v(" "),n("span",{staticStyle:{display:"inline-block","margin-left":"40px",color:"coral","font-size":"13px"}},[t._v("\n      如果Pod无法正常启动\n      "),n("el-popover",{attrs:{placement:"top-start",width:"600",trigger:"click"}},[n("div",{staticStyle:{"line-height":"15px","font-size":"13px"}},[t._v('\n          1. 如果Pod一直处于"调度"状态，则说明集群资源不足。先确认Pod的CPU和内存资源配置是否合理。若配置合理，请联系集群管理员处理。 '),n("br"),n("br"),t._v('\n          2. 如果Pod一直处于"准备"状态，请查看Pod事件信息，确认是否存在具体的报错信息。'),n("br"),n("br"),t._v('\n          3. 如果Pod一直处于"启动"状态，请查看Pod启动日志，确认Tomcat启动状态，查看是否有业务报错信息。'),n("br"),n("br"),t._v('\n          4. 如果Pod发生了重启的话，请查看Pod重启前的日志（点击启动日志按钮，再勾选"重启前日志"复选框），查找可能得报错信息。'),n("br")]),t._v(" "),n("el-button",{staticStyle:{"font-size":"13px"},attrs:{slot:"reference",type:"text"},slot:"reference"},[t._v("请查看排查手册")])],1)],1)]),t._v(" "),n("el-card",{staticClass:"box-card"},[t.job.id?n("deploy-pod-list",{attrs:{app:t.job.params.app,cluster:t.job.params.cluster,namespace:t.job.params.namespace,timestamp:new Date(t.job.createdAt).getTime()}}):t._e()],1),t._v(" "),n("div",{staticStyle:{"margin-top":"20px","font-size":"14px","font-weight":"bold",color:"rgb(64, 158, 255)","padding-bottom":"3px"}},[t._v("日志")]),t._v(" "),n("el-card",{staticClass:"box-card"},[n("el-tabs",{attrs:{"tab-position":"top"}},t._l(t.tasks,(function(e,o){return n("el-tab-pane",{attrs:{label:e.title}},[n("div",[n("pre",{staticClass:"el-collapse-item__content",staticStyle:{"white-space":"pre-wrap","padding-left":"10px"}},[t._v(t._s(e.output))])])])})),1),t._v(" "),n("div",{staticStyle:{"padding-top":"10px"}})],1),t._v(" "),n("div",[n("el-backtop")],1)],1)},a=[],s=n("2d63"),i=(n("6762"),n("2fdb"),n("76fe")),r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"pod-simple-table-wrapper"},[n("div",{staticStyle:{"text-align":"center"}},[t.podPendingAlert?n("div",{staticStyle:{display:"inline-block",width:"480px","text-align":"left"}},[n("el-alert",{attrs:{title:"有pod长时间未调度到宿主机上",type:"warning","show-icon":""}},[n("div",[n("div",[t._v("当资源池资源不足时，新的pod将无法正常调度到宿主机上")]),t._v(" "),n("div",[t._v("1. 如果必须要保障此次发布平滑性，可联系 @金哲玉 @吴志辉 协助处理")]),t._v(" "),n("div",[t._v("2. 如果允许此次发布造成业务抖动的话，可点击\n            "),n("el-button",{attrs:{type:"text",size:"mini"},on:{click:t.redeployWithRecreate}},[t._v("使用重建升级")])],1),t._v(" "),n("div",[t._v("重建升级: 会先删除旧版本pod，释放资源后再创建新版本pod")])])])],1):t._e()]),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.newPods.concat(t.oldPods),size:"small","expand-row-keys":t.expandRowKeys,"row-key":"name"}},[n("el-table-column",{attrs:{type:"expand",width:"40"},scopedSlots:t._u([{key:"default",fn:function(t){return[n("pod-event",{attrs:{cluster:t.row.cluster,namespace:t.row.namespace,pod:t.row.name}})]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){return["new"===t.podVersionDesc(e.row.name)?n("el-tag",{attrs:{size:"mini",effect:"plain"}},[t._v(t._s(t.podVersionDesc(e.row.name)))]):n("el-tag",{attrs:{size:"mini",effect:"plain",type:"info"}},[t._v(t._s(t.podVersionDesc(e.row.name)))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"实例名",prop:"name"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.name))])]}}])}),t._v(" "),n("el-table-column",{attrs:{width:"100"},scopedSlots:t._u([{key:"header",fn:function(e){return[t._v("\n        状态\n        "),n("el-tooltip",{attrs:{effect:"light",placement:"top"}},[n("div",{attrs:{slot:"content"},slot:"content"},[n("el-image",{staticStyle:{"max-width":"800px"},attrs:{src:"images/pod-status.svg",alt:"pod状态"}})],1),t._v(" "),n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)]}},{key:"default",fn:function(e){return[n("span",{class:t.podStatusClass(e.row.statusDesc)}),t._v("\n        "+t._s(e.row.statusDesc)+"\n      ")]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"运行版本",prop:"deployTag","show-overflow-tooltip":""}}),t._v(" "),n("el-table-column",{attrs:{label:"重启数",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.restartCount))]),t._v(" "),e.row.restartCount>0?n("div",{staticStyle:{"font-size":"10px",color:"#888"}},[t._v("重启原因: "+t._s(e.row.restartReason))]):t._e()]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"Pod IP",prop:"podIP","show-overflow-tooltip":""}}),t._v(" "),n("el-table-column",{attrs:{label:"Host IP",prop:"hostIP","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.hostIP))]),t._v(" "),e.row.resourcePool?n("div",{staticStyle:{"font-size":"10px",color:"#888"}},[t._v("资源池: "+t._s(e.row.resourcePool))]):t._e()]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"创建时间",prop:"createTime",width:"140"}}),t._v(" "),n("el-table-column",{attrs:{label:"",width:"160",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"事件列表",placement:"top"}},[n("el-button",{staticStyle:{padding:"5px"},attrs:{circle:"",size:"small"},on:{click:function(n){return t.rowExpand(e.row.name)}}},[n("svg-icon",{staticStyle:{"font-size":"1.5em"},attrs:{"icon-class":"event"}})],1)],1),t._v(" "),n("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"启动日志",placement:"top"}},[n("el-button",{staticStyle:{padding:"5px"},attrs:{circle:"",size:"small"},on:{click:function(n){return t.podStdoutLog(e.row)}}},[n("svg-icon",{staticStyle:{"font-size":"1.5em"},attrs:{"icon-class":"log"}})],1)],1),t._v(" "),n("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"进入容器",placement:"top"}},[n("el-button",{staticStyle:{padding:"5px"},attrs:{circle:"",size:"small"},on:{click:function(n){return t.podShell(e.row)}}},[n("svg-icon",{staticStyle:{"font-size":"1.5em"},attrs:{"icon-class":"console"}})],1)],1)]}}])})],1),t._v(" "),n("el-dialog",{attrs:{title:"容器启动日志（标准输出)",visible:t.podStdoutVisible,top:"5vh","close-on-click-modal":!1,width:"70%","append-to-body":"",center:""},on:{"update:visible":function(e){t.podStdoutVisible=e},close:function(e){t.podStdout.pod=null}}},[n("div",{staticStyle:{"margin-top":"-30px"}},[n("pod-stdout",{attrs:{cluster:this.podStdout.cluster,namespace:this.podStdout.namespace,pod:this.podStdout.pod,containers:this.podStdout.containers}})],1)])],1)},c=[],l=n("75fc"),d=(n("7f7f"),n("c5f6"),n("c1df")),u=n.n(d),p=n("cf89"),f=n("8504"),m=n("6e36"),b=n("1262"),v=n("a527"),h={name:"DeployPodList",components:{PodEvent:b["a"],PodExpand:m["a"],PodStdout:p["a"]},props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},app:{type:String,required:!0},timestamp:{type:Number,required:!0}},data:function(){return{loading:!1,execution:{},oldPods:[],newPods:[],expandRowKeys:[],podPendingAlert:!1,podStdoutVisible:!1,podStdout:{cluster:"",namespace:"",pod:"",containers:[]}}},watch:{},computed:{},mounted:function(){this.loadPods()},beforeDestroy:function(){},methods:{loadPods:function(){var t=this;this.loading=!0;var e=this;Object(v["h"])(this.cluster,this.namespace,this.app).then((function(e){t.oldPods=[],t.newPods=[];var n,o=Object(s["a"])(e.data);try{for(o.s();!(n=o.n()).done;){var a=n.value;new Date(a.createTime).getTime()<t.timestamp?t.oldPods.push(a):t.newPods.push(a)}}catch(i){o.e(i)}finally{o.f()}})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1,t.showAlert(),e.isReloadPods()&&setTimeout((function(){e.loadPods()}),2e4)}))},rowExpand:function(t){this.expandRowKeys&&this.expandRowKeys[0]===t?this.expandRowKeys=[]:this.expandRowKeys=[t]},isReloadPods:function(){if(this.oldPods.length>0)return!0;var t,e=!1,n=Object(s["a"])(this.newPods);try{for(n.s();!(t=n.n()).done;){var o=t.value;if(!o.ready){e=!0;break}}}catch(a){n.e(a)}finally{n.f()}return e},showAlert:function(){var t=!1;if(this.oldPods&&this.oldPods.length>0&&this.newPods&&this.newPods.length>0){var e,n=Object(s["a"])(this.newPods);try{for(n.s();!(e=n.n()).done;){var o=e.value;if("调度中"===o.statusDesc){var a=u()(),i=u()(o.createTime),r=a.diff(i,"seconds");if(r>120){console.log("podCreateTime pending time: "+r),t=!0;break}}}}catch(c){n.e(c)}finally{n.f()}}this.podPendingAlert=t},podVersionDesc:function(t){var e=this.oldPods.filter((function(e){return e.name===t}));return e&&e.length>0?"old":(e=this.newPods.filter((function(e){return e.name===t})),e&&e.length>0?"new":"--")},redeployWithRecreate:function(){var t=this;this.$confirm("此操作会先删除旧版本pod，释放资源后再创建新版本pod。在新版pod能正常提供服务前，当前服务将会处于不可用状态。确认是否继续？","重建升级",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){if(!t.oldPods||t.oldPods.length<1)t.$message.warning("没有旧版pod，无法执行当前操作");else{var e=t.oldPods[0];Object(f["i"])({cluster:e.cluster,namespace:e.namespace,app:e.labelApp}).then((function(e){t.$message.success("操作成功")})).catch((function(e){t.$message.error(e.message),t.stopReloadTimer()}))}})).catch((function(){}))},podStdoutLog:function(t){this.podStdoutVisible=!0,this.podStdout.cluster=t.cluster,this.podStdout.namespace=t.namespace,this.podStdout.pod=t.name,this.podStdout.containers=[t.container0Name].concat(Object(l["a"])(t.initContainersName))},podShell:function(t){var e="/api/page/redirect?type=webShell&cluster=".concat(t.cluster,"&namespace=").concat(t.namespace,"&app=").concat(t.labelApp,"&pods=").concat(t.name);window.open(e)},podStatusClass:function(t){if(t){if("运行中"===t)return"pod-status-green";if(["调度中","准备中","启动中"].includes(t))return"pod-status-orange"}return"pod-status-red"}}},g=h,j=(n("ffff8"),n("2877")),_=Object(j["a"])(g,r,c,!1,null,null,null),y=_.exports,w={data:function(){return{timerId:null,jobLoading:!0,job:{params:{}},tasks:[],activeNames:[],runningExecutions:[],jenkinsTable:"jenkinsTab2"}},components:{DeployPodList:y},computed:{},beforeDestroy:function(){this.timerId&&(clearTimeout(this.timerId),console.log("clear timer, id:"+this.timerId))},mounted:function(){var t=this.$route.query.jobId;t&&this.loadJob(t)},methods:{loadJob:function(t){var e=this;this.jobLoading=!0,Object(i["g"])(t).then((function(n){e.job=n.data,e.loadTasks(t);var o=e.job.status.toUpperCase(),a=e;"RUNNING"===o&&(e.timerId=setTimeout((function(){a.loadJob(t)}),5e3))})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.jobLoading=!1}))},loadTasks:function(t){var e=this;Object(i["h"])(t).then((function(t){e.tasks=t.data,e.modifyStages()})).catch((function(t){e.$message.error(t.message)}))},stepClick:function(t){this.activeNames.includes(t)?this.activeNames=[]:this.activeNames=[t]},modifyStages:function(){var t,e=Object(s["a"])(this.tasks);try{for(e.s();!(t=e.n()).done;){var n=t.value;n.status=this.toStepStatus(n.status),"process"===n.status?n.icon="el-icon-loading":n.icon=""}}catch(o){e.e(o)}finally{e.f()}},cancel:function(){var t=this;this.$confirm("取消后后端新旧实例还会继续进行替换。是否继续取消当前发布？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(i["d"])(t.job.id).then((function(e){t.$message.success("操作成功！"),t.loadJob(t.job.id)})).catch((function(e){t.$message.error(e.message)}))})).catch((function(t){console.error(t)}))},setJobFailed:function(){var t=this;Object(i["l"])(this.job.id,"fail").then((function(e){t.$message.success("操作成功！"),t.loadJob(t.job.id)})).catch((function(e){t.$message.error(e.message)}))},setJobSuccess:function(){var t=this;Object(i["l"])(this.job.id,"success").then((function(e){t.$message.success("操作成功！"),t.loadJob(t.job.id)})).catch((function(e){t.$message.error(e.message)}))},redo:function(){var t=this;this.$confirm("确认要使用当前参数重发吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(i["j"])(t.job.id).then((function(e){var n=e.data.id,o=t.$router.resolve({query:{jobId:n}});window.location.href=o.href,window.location.reload()})).catch((function(e){t.$message.error(e.message)}))})).catch((function(t){console.error(t)}))},podPage:function(){var t=this.$router.resolve({name:"pod-index",query:{cluster:this.job.params.cluster,namespace:this.job.params.namespace,app:this.job.params.app}});window.open(t.href,"_blank")},pipelinePage:function(){this.$router.push({name:"cicd-app-deploy",query:{app:this.job.params.app}})},jobDetailPage:function(t){var e=this;Object(i["g"])(t).then((function(n){var o=n.data,a="CD"===o.type?"cicd-app-deploy-detail":"cicd-image-build-detail",s=e.$router.resolve({name:a,query:{jobId:t}});window.open(s.href,"_blank")})).catch((function(t){e.$message.error(t.message)}))},toStepStatus:function(t){var e="";switch(t){case"WAIT":case"SKIP":case"CANCEL":e="wait";break;case"RUNNING":e="process";break;case"FAIL":e="error";break;case"SUCCESS":e="success";break;default:e="finish"}return e}}},S=w,x=(n("bb54"),Object(j["a"])(S,o,a,!1,null,null,null));e["default"]=x.exports},4678:function(t,e,n){var o={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"2554","./bs.js":"2554","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"73332","./en-il.js":"73332","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e9","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e9","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function a(t){var e=s(t);return n(e)}function s(t){var e=o[t];if(!(e+1)){var n=new Error("Cannot find module '"+t+"'");throw n.code="MODULE_NOT_FOUND",n}return e}a.keys=function(){return Object.keys(o)},a.resolve=s,t.exports=a,a.id="4678"},5147:function(t,e,n){var o=n("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[o]=!1,!"/./"[t](e)}catch(a){}}return!0}},"5dbc":function(t,e,n){var o=n("d3f4"),a=n("8b97").set;t.exports=function(t,e,n){var s,i=e.constructor;return i!==n&&"function"==typeof i&&(s=i.prototype)!==n.prototype&&o(s)&&a&&a(t,s),t}},6762:function(t,e,n){"use strict";var o=n("5ca1"),a=n("c366")(!0);o(o.P,"Array",{includes:function(t){return a(this,t,arguments.length>1?arguments[1]:void 0)}}),n("9c6c")("includes")},"6e36":function(t,e,n){"use strict";var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"pod-expand"},[t.pod?n("div",{staticClass:"expand-wrapper"},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form",{attrs:{"label-position":"left","label-width":"80px",size:"mini"}},[n("el-form-item",{staticClass:"expand-wrapper-item",attrs:{label:"实例名"}},[n("span",[t._v(t._s(t.pod.name))])]),t._v(" "),n("el-form-item",{staticClass:"expand-wrapper-item",attrs:{label:"集群/环境"}},[n("span",[t._v(t._s(t.pod.cluster)+"/"+t._s(t.pod.namespace))])]),t._v(" "),n("el-form-item",{attrs:{label:"运行版本"}},[n("span",[t._v(t._s(t.pod.deployTag))])]),t._v(" "),n("el-form-item",{attrs:{label:"容器镜像"}},[n("span",[t._v(t._s(t.pod.container0Image))])]),t._v(" "),n("el-form-item",{attrs:{label:"状态"}},[t._v("\n            "+t._s(t.pod.statusDesc)+"\n            "),n("span",{staticStyle:{"padding-left":"10px"}},[t._v(" |\n               "),n("el-tag",{attrs:{effect:"plain",size:"mini",type:"info"}},[t._v("PodPhase: "+t._s(t.pod.phase))]),t._v(" "),n("el-tag",{attrs:{effect:"plain",size:"mini",type:"info"}},[t._v("podReady: "+t._s(t.pod.ready))]),t._v(" "),n("el-tag",{attrs:{effect:"plain",size:"mini",type:"info"}},[t._v("Container0Status: "+t._s(t.pod.container0Status))])],1)]),t._v(" "),n("el-form-item",{attrs:{label:"所在机器"}},[n("div",[n("span",[t._v(t._s(t.pod.hostIP))]),t._v(" "),n("span",[t._v("\n                （资源池: "+t._s(t.pod.resourcePool?t.pod.resourcePool:"Common")+" ）\n              ")])])]),t._v(" "),n("el-form-item",{attrs:{label:"资源配置"}},[n("span",[n("b",{staticStyle:{"padding-right":"5px"}},[t._v("CPU: ")]),t._v(t._s((t.pod.requestCpu/1e3).toFixed(2))+" - "+t._s((t.pod.limitCpu/1e3).toFixed(2))+" ")]),t._v(" "),n("span",[n("b",{staticStyle:{"padding-right":"5px","padding-left":"30px"}},[t._v("内存 (MB): ")]),t._v("\n              "+t._s(Math.floor(t.pod.requestMemory/1024/1024))+" - "+t._s(Math.floor(t.pod.limitMemory/1024/1024))+"\n            ")])]),t._v(" "),n("el-form-item",{attrs:{label:"重启次数"}},[n("span",[t._v(t._s(t.pod.restartCount))])]),t._v(" "),t.pod.restartCount>0?n("el-form-item",{attrs:{label:"最近重启时间"}},[n("span",[t._v(t._s(t.pod.restartTime))])]):t._e(),t._v(" "),t.pod.restartCount>0?n("el-form-item",{attrs:{label:"最近重启说明"}},[n("span",[t._v("Reason: "+t._s(t.pod.restartReason||"--"))]),t._v(" "),n("span",{staticStyle:{"padding-left":"20px"}},[t._v("ExitCode: "+t._s(t.pod.restartCode))])]):t._e()],1)],1),t._v(" "),n("el-col",{attrs:{span:12}},[n("div",{staticStyle:{color:"#606266","font-weight":"bold","padding-left":"15px","line-height":"40px"}},[t._v("事件列表")]),t._v(" "),n("pod-event",{attrs:{cluster:t.pod.cluster,namespace:t.pod.namespace,pod:t.pod.name}})],1)],1)],1):t._e()])},a=[],s=n("1262"),i={components:{PodEvent:s["a"]},props:{pod:{type:Object,default:function(){return{}},required:!0}},created:function(){},computed:{},data:function(){return{}},methods:{}},r=i,c=(n("8f56"),n("2877")),l=Object(c["a"])(r,o,a,!1,null,null,null);e["a"]=l.exports},"75fc":function(t,e,n){"use strict";var o=n("a745"),a=n.n(o),s=n("db2a");function i(t){if(a()(t))return Object(s["a"])(t)}var r=n("67bb"),c=n.n(r),l=n("5d58"),d=n.n(l),u=n("774e"),p=n.n(u);function f(t){if("undefined"!==typeof c.a&&null!=t[d.a]||null!=t["@@iterator"])return p()(t)}var m=n("e630");function b(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(t){return i(t)||f(t)||Object(m["a"])(t)||b()}n.d(e,"a",(function(){return v}))},"76fe":function(t,e,n){"use strict";n.d(e,"k",(function(){return a})),n.d(e,"i",(function(){return s})),n.d(e,"a",(function(){return i})),n.d(e,"e",(function(){return r})),n.d(e,"b",(function(){return c})),n.d(e,"f",(function(){return l})),n.d(e,"c",(function(){return d})),n.d(e,"g",(function(){return u})),n.d(e,"h",(function(){return p})),n.d(e,"d",(function(){return f})),n.d(e,"l",(function(){return m})),n.d(e,"j",(function(){return b}));var o=n("b775");function a(t){return Object(o["a"])({url:"/v1/job/search",method:"post",data:t})}function s(t){return Object(o["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:t}})}function i(t){return Object(o["a"])({url:"/v1/job/build-image",method:"post",data:t})}function r(t){return Object(o["a"])({url:"/v1/job/deploy-app",method:"post",data:t})}function c(t){return Object(o["a"])({url:"/v1/job/build-and-deploy",method:"post",data:t})}function l(t,e,n,a){return Object(o["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:t,namespace:e,app:n,remark:a}})}function d(t,e,n,a){return Object(o["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:t,namespace:e,app:n,remark:a}})}function u(t){return Object(o["a"])({url:"/v1/job/detail",method:"get",params:{id:t}})}function p(t){return Object(o["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:t}})}function f(t){return Object(o["a"])({url:"/v1/job/cancel",method:"put",data:{id:t}})}function m(t,e){return Object(o["a"])({url:"/v1/job/update-status?id=".concat(t,"&status=").concat(e),method:"put"})}function b(t){return Object(o["a"])({url:"/v1/job/redo",method:"post",data:{id:t}})}},8504:function(t,e,n){"use strict";n.d(e,"g",(function(){return a})),n.d(e,"a",(function(){return s})),n.d(e,"h",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"b",(function(){return c})),n.d(e,"i",(function(){return l})),n.d(e,"d",(function(){return d})),n.d(e,"f",(function(){return u})),n.d(e,"e",(function(){return p}));var o=n("b775");function a(t,e){return Object(o["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function s(t,e,n){return Object(o["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:n}})}function i(t){return Object(o["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function r(t,e,n){return Object(o["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:n}})}function c(t){return Object(o["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function l(t){return Object(o["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function d(t,e,n){return Object(o["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:n}})}function u(t){return Object(o["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function p(t,e,n,a,s){return Object(o["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:n,revision:a,deployTag:s||""}})}},"8b97":function(t,e,n){var o=n("d3f4"),a=n("cb7c"),s=function(t,e){if(a(t),!o(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,o){try{o=n("9b43")(Function.call,n("11e9").f(Object.prototype,"__proto__").set,2),o(t,[]),e=!(t instanceof Array)}catch(a){e=!0}return function(t,n){return s(t,n),e?t.__proto__=n:o(t,n),t}}({},!1):void 0),check:s}},"8f56":function(t,e,n){"use strict";n("e0c2")},9093:function(t,e,n){var o=n("ce10"),a=n("e11e").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return o(t,a)}},a527:function(t,e,n){"use strict";n.d(e,"h",(function(){return a})),n.d(e,"d",(function(){return s})),n.d(e,"i",(function(){return i})),n.d(e,"e",(function(){return r})),n.d(e,"g",(function(){return c})),n.d(e,"c",(function(){return l})),n.d(e,"k",(function(){return d})),n.d(e,"l",(function(){return u})),n.d(e,"m",(function(){return p})),n.d(e,"o",(function(){return f})),n.d(e,"f",(function(){return m})),n.d(e,"r",(function(){return b})),n.d(e,"b",(function(){return v})),n.d(e,"a",(function(){return h})),n.d(e,"p",(function(){return g})),n.d(e,"q",(function(){return j})),n.d(e,"n",(function(){return _})),n.d(e,"j",(function(){return y}));n("96cf"),n("3b8d");var o=n("b775");function a(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/list",method:"get",params:{cluster:t,namespace:e,app:n}})}function s(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/deregister/list-by-app",method:"get",params:{cluster:t,namespace:e,app:n}})}function i(t,e){return Object(o["a"])({url:"/v1/k8s/pod/list-by-env",method:"get",params:{cluster:t,namespace:e}})}function r(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/detail",method:"get",params:{cluster:t,namespace:e,pod:n}})}function c(t,e,n,a,s,i){return Object(o["a"])({url:"/v1/k8s/pod/stdout",method:"get",params:{cluster:t,namespace:e,pod:n,container:a,tailLines:s,previous:i}})}function l(t,e,n,o,a){var s="/api/v1/k8s/pod/stdout/download?cluster=".concat(t,"&namespace=").concat(e,"&pod=").concat(n,"&container=").concat(o,"&tailLines=").concat(a,'&_time="')+(new Date).getTime();window.open(s)}function d(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/delete",method:"delete",params:{cluster:t,namespace:e,pod:n}})}function u(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/deregister",method:"put",params:{cluster:t,namespace:e,pod:n}})}function p(t){return Object(o["a"])({url:"/v1/k8s/pod/deregister/list-by-cluster",method:"get",params:{cluster:t}})}function f(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/version/retain",method:"put",params:{cluster:t,namespace:e,pod:n}})}function m(t){return Object(o["a"])({url:"/v1/k8s/pod/file/list",method:"get",params:t})}function b(t){return Object(o["a"])({url:"/v1/k8s/pod/file/ready",method:"post",data:t})}function v(t,e){window.open("/api/v1/k8s/pod/file/download?fileId="+t+"&fileName="+e+"&_time="+(new Date).getTime())}function h(t){return Object(o["a"])({url:"/v1/k8s/pod/file/archive",method:"post",data:t})}function g(t){window.open("/api/v1/k8s/pod/file/preview?fileId="+t)}function j(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/cms/list",method:"get",params:{cluster:t,namespace:e,pod:n}})}function _(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/events",method:"get",params:{cluster:t,namespace:e,pod:n}})}function y(t,e,n){return Object(o["a"])({url:"/v1/k8s/pod/open-pyroscope",method:"post",data:{cluster:t,namespace:e,pod:n}})}},aa77:function(t,e,n){var o=n("5ca1"),a=n("be13"),s=n("79e5"),i=n("fdef"),r="["+i+"]",c="​",l=RegExp("^"+r+r+"*"),d=RegExp(r+r+"*$"),u=function(t,e,n){var a={},r=s((function(){return!!i[t]()||c[t]()!=c})),l=a[t]=r?e(p):i[t];n&&(a[n]=l),o(o.P+o.F*r,"String",a)},p=u.trim=function(t,e){return t=String(a(t)),1&e&&(t=t.replace(l,"")),2&e&&(t=t.replace(d,"")),t};t.exports=u},aae3:function(t,e,n){var o=n("d3f4"),a=n("2d95"),s=n("2b4c")("match");t.exports=function(t){var e;return o(t)&&(void 0!==(e=t[s])?!!e:"RegExp"==a(t))}},bb54:function(t,e,n){"use strict";n("3336")},c5f6:function(t,e,n){"use strict";var o=n("7726"),a=n("69a8"),s=n("2d95"),i=n("5dbc"),r=n("6a99"),c=n("79e5"),l=n("9093").f,d=n("11e9").f,u=n("86cc").f,p=n("aa77").trim,f="Number",m=o[f],b=m,v=m.prototype,h=s(n("2aeb")(v))==f,g="trim"in String.prototype,j=function(t){var e=r(t,!1);if("string"==typeof e&&e.length>2){e=g?e.trim():p(e,3);var n,o,a,s=e.charCodeAt(0);if(43===s||45===s){if(n=e.charCodeAt(2),88===n||120===n)return NaN}else if(48===s){switch(e.charCodeAt(1)){case 66:case 98:o=2,a=49;break;case 79:case 111:o=8,a=55;break;default:return+e}for(var i,c=e.slice(2),l=0,d=c.length;l<d;l++)if(i=c.charCodeAt(l),i<48||i>a)return NaN;return parseInt(c,o)}}return+e};if(!m(" 0o1")||!m("0b1")||m("+0x1")){m=function(t){var e=arguments.length<1?0:t,n=this;return n instanceof m&&(h?c((function(){v.valueOf.call(n)})):s(n)!=f)?i(new b(j(e)),n,m):j(e)};for(var _,y=n("9e1e")?l(b):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),w=0;y.length>w;w++)a(b,_=y[w])&&!a(m,_)&&u(m,_,d(b,_));m.prototype=v,v.constructor=m,n("2aba")(o,f,m)}},cf89:function(t,e,n){"use strict";var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.stdout.loading,expression:"stdout.loading"}],staticClass:"pod-stdout"},[n("div",{staticStyle:{position:"relative"}},[n("div",{staticStyle:{"font-weight":"bold",float:"left","line-height":"40px"}},[t._v(t._s(this.pod)+" /\n      "),n("el-select",{staticStyle:{width:"240px"},attrs:{size:"mini",placeholder:"请选择容器"},on:{change:t.loadStdoutLog},model:{value:t.container,callback:function(e){t.container=t._n(e)},expression:"container"}},t._l(t.containers,(function(t){return n("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1),t._v(" "),n("div",{staticStyle:{float:"right","margin-right":"20px"}},[n("span",[n("el-checkbox",{on:{change:t.loadStdoutLog},model:{value:t.stdout.previous,callback:function(e){t.$set(t.stdout,"previous",e)},expression:"stdout.previous"}},[t._v("重启前日志")])],1),t._v(" "),n("span",{staticStyle:{"margin-left":"20px"}},[t._v("\n            行数:\n            "),n("el-select",{staticStyle:{width:"120px"},on:{change:t.loadStdoutLog},model:{value:t.stdout.tailLines,callback:function(e){t.$set(t.stdout,"tailLines",t._n(e))},expression:"stdout.tailLines"}},[n("el-option",{attrs:{label:"2000",value:"2000"}}),t._v(" "),n("el-option",{attrs:{label:"5000",value:"5000"}}),t._v(" "),n("el-option",{attrs:{label:"10000",value:"10000"}}),t._v(" "),n("el-option",{attrs:{label:"50000",value:"50000"}})],1)],1),t._v(" "),n("span",{staticStyle:{display:"none"}},[t._v("\n        自动刷新("+t._s(t.stdout.reloadPeriod)+"秒):\n      "),n("el-switch",{on:{change:t.autoReloadSwitch},model:{value:t.stdout.autoReload,callback:function(e){t.$set(t.stdout,"autoReload",e)},expression:"stdout.autoReload"}})],1),t._v(" "),n("el-button",{staticClass:"el-icon-refresh",staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(e){return t.loadStdoutLog()}}},[t._v("刷新\n      ")]),t._v(" "),n("el-button",{staticClass:"el-icon-download",staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(e){return t.podStdoutLogDownload()}}},[t._v("下载\n      ")])],1),t._v(" "),n("div",{staticStyle:{clear:"both"}})]),t._v(" "),n("div",{staticStyle:{"text-align":"right","margin-right":"5px"}},[t._v("加载时间: "+t._s(t.stdout.lastReloadTime))]),t._v(" "),n("pre",{staticClass:"stdout-log-content",attrs:{id:"stdout-log-content"}},[t._v(t._s(t.stdout.content))])])},a=[],s=n("a527"),i={name:"PodStdout",props:{cluster:{type:String,required:!0},namespace:{type:String,required:!0},pod:{type:String,required:!1},containers:{type:Array,required:!1,default:function(){return[]}}},data:function(){return{container:this.containers[0],stdout:{visible:!1,loading:!1,autoReload:!1,previous:!1,tailLines:2e3,reloadPeriod:10,reloadTimer:null,content:"",lastReloadTime:"--"}}},watch:{pod:function(t,e){this.container=this.containers[0],this.loadStdoutLog()}},computed:{},mounted:function(){this.loadStdoutLog()},beforeDestroy:function(){this.stopReloadTimer()},methods:{showStdoutLogDialog:function(){this.stdout.visible=!0,this.loadStdoutLog(),this.stdout.autoReload&&this.startReloadTimer()},loadStdoutLog:function(){var t=this;this.pod&&(console.log("load pod ".concat(this.pod," stdout log")),this.stdout.loading=!0,Object(s["g"])(this.cluster,this.namespace,this.pod,this.container,this.stdout.tailLines,this.stdout.previous).then((function(e){t.stdout.content=e.data;var n=t;setTimeout((function(){n.scrollStdoutLogView()}),200),setTimeout((function(){n.scrollStdoutLogView()}),500),setTimeout((function(){n.scrollStdoutLogView()}),700),t.stdout.lastReloadTime=(new Date).toLocaleTimeString()})).catch((function(e){t.$message.error(e.message),t.stopReloadTimer()})).finally((function(){t.stdout.loading=!1})))},podStdoutLogDownload:function(){Object(s["c"])(this.cluster,this.namespace,this.pod,this.container,this.stdout.tailLines)},stopReload:function(){this.stdout.autoReload=!1,this.stopReloadTimer()},scrollStdoutLogView:function(){var t=document.getElementById("stdout-log-content");t.scrollTop=t.scrollHeight},startReloadTimer:function(){this.stdout.reloadTimer&&this.stopReloadTimer();var t=this;this.stdout.reloadTimer=setInterval((function(){t.loadStdoutLog()}),1e3*t.stdout.reloadPeriod),console.log("started pod stdout log reload timer :"+this.stdout.reloadTimer)},stopReloadTimer:function(){clearInterval(this.stdout.reloadTimer),console.log("stopped pod stdout log reload timer :"+this.stdout.reloadTimer)},autoReloadSwitch:function(t){this.stdout.autoReload=t,t?this.startReloadTimer():this.stopReloadTimer()}}},r=i,c=(n("fbec"),n("2877")),l=Object(c["a"])(r,o,a,!1,null,"6ff71a9f",null);e["a"]=l.exports},d2c8:function(t,e,n){var o=n("aae3"),a=n("be13");t.exports=function(t,e,n){if(o(e))throw TypeError("String#"+n+" doesn't accept regex!");return String(a(t))}},e0c2:function(t,e,n){},e18c:function(t,e,n){},fbec:function(t,e,n){"use strict";n("e18c")},fdef:function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"},ffff8:function(t,e,n){"use strict";n("0736")}}]);